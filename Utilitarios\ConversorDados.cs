using System.Globalization;
using System.Text;

namespace SistemaMFT.Utilitarios
{
    /// <summary>
    /// Classe utilitária para conversões de dados específicas do sistema MFT
    /// Contém métodos para conversão de datas, valores monetários e formatação de strings
    /// </summary>
    public static class ConversorDados
    {
        /// <summary>
        /// Converte uma data no formato string DDMMYYYY para DateTime
        /// </summary>
        /// <param name="dataString">Data no formato DDMMYYYY</param>
        /// <returns>Data convertida para DateTime</returns>
        /// <exception cref="ArgumentException">Quando a data está em formato inválido</exception>
        public static DateTime ConverterDataDDMMYYYY(string dataString)
        {
            if (string.IsNullOrEmpty(dataString) || dataString.Length != 8)
            {
                throw new ArgumentException("Data deve ter 8 dígitos no formato DDMMYYYY", nameof(dataString));
            }

            if (!int.TryParse(dataString.Substring(0, 2), out int dia) ||
                !int.TryParse(dataString.Substring(2, 2), out int mes) ||
                !int.TryParse(dataString.Substring(4, 4), out int ano))
            {
                throw new ArgumentException("Data contém caracteres inválidos", nameof(dataString));
            }

            try
            {
                return new DateTime(ano, mes, dia);
            }
            catch (ArgumentOutOfRangeException)
            {
                throw new ArgumentException($"Data inválida: {dia:00}/{mes:00}/{ano}", nameof(dataString));
            }
        }

        /// <summary>
        /// Converte uma data no formato string MMDDYYYY para DateTime
        /// </summary>
        /// <param name="dataString">Data no formato MMDDYYYY</param>
        /// <returns>Data convertida para DateTime</returns>
        /// <exception cref="ArgumentException">Quando a data está em formato inválido</exception>
        public static DateTime ConverterDataMMDDYYYY(string dataString)
        {
            if (string.IsNullOrEmpty(dataString) || dataString.Length != 8)
            {
                throw new ArgumentException("Data deve ter 8 dígitos no formato MMDDYYYY", nameof(dataString));
            }

            if (!int.TryParse(dataString.Substring(0, 2), out int mes) ||
                !int.TryParse(dataString.Substring(2, 2), out int dia) ||
                !int.TryParse(dataString.Substring(4, 4), out int ano))
            {
                throw new ArgumentException("Data contém caracteres inválidos", nameof(dataString));
            }

            try
            {
                return new DateTime(ano, mes, dia);
            }
            catch (ArgumentOutOfRangeException)
            {
                throw new ArgumentException($"Data inválida: {mes:00}/{dia:00}/{ano}", nameof(dataString));
            }
        }

        /// <summary>
        /// Converte DateTime para formato DDMMYYYY
        /// </summary>
        /// <param name="data">Data para converter</param>
        /// <returns>Data no formato DDMMYYYY</returns>
        public static string ConverterParaDDMMYYYY(DateTime data)
        {
            return data.ToString("ddMMyyyy");
        }

        /// <summary>
        /// Converte DateTime para formato MMDDYYYY
        /// </summary>
        /// <param name="data">Data para converter</param>
        /// <returns>Data no formato MMDDYYYY</returns>
        public static string ConverterParaMMDDYYYY(DateTime data)
        {
            return data.ToString("MMddyyyy");
        }

        /// <summary>
        /// Converte valor monetário em string para decimal
        /// As últimas 2 posições representam centavos
        /// </summary>
        /// <param name="valorString">Valor como string</param>
        /// <returns>Valor convertido para decimal</returns>
        public static decimal ConverterValorMonetario(string valorString)
        {
            if (string.IsNullOrEmpty(valorString))
                return 0;

            var valorLimpo = valorString.Trim();
            if (string.IsNullOrEmpty(valorLimpo))
                return 0;

            // Remove zeros à esquerda
            valorLimpo = valorLimpo.TrimStart('0');
            if (string.IsNullOrEmpty(valorLimpo))
                return 0;

            if (!long.TryParse(valorLimpo, out long valor))
                return 0;

            // As últimas 2 posições são centavos
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte decimal para string de valor monetário
        /// </summary>
        /// <param name="valor">Valor decimal</param>
        /// <param name="tamanhoTotal">Tamanho total da string (incluindo zeros à esquerda)</param>
        /// <returns>Valor formatado como string</returns>
        public static string ConverterParaValorMonetario(decimal valor, int tamanhoTotal)
        {
            var valorCentavos = (long)(valor * 100);
            return valorCentavos.ToString().PadLeft(tamanhoTotal, '0');
        }

        /// <summary>
        /// Formata string para tamanho fixo, preenchendo com espaços à direita
        /// </summary>
        /// <param name="texto">Texto para formatar</param>
        /// <param name="tamanho">Tamanho desejado</param>
        /// <returns>String formatada</returns>
        public static string FormatarTextoTamanhoFixo(string texto, int tamanho)
        {
            if (string.IsNullOrEmpty(texto))
                return new string(' ', tamanho);

            if (texto.Length > tamanho)
                return texto.Substring(0, tamanho);

            return texto.PadRight(tamanho, ' ');
        }

        /// <summary>
        /// Formata número para tamanho fixo, preenchendo com zeros à esquerda
        /// </summary>
        /// <param name="numero">Número para formatar</param>
        /// <param name="tamanho">Tamanho desejado</param>
        /// <returns>Número formatado como string</returns>
        public static string FormatarNumeroTamanhoFixo(int numero, int tamanho)
        {
            return numero.ToString().PadLeft(tamanho, '0');
        }

        /// <summary>
        /// Remove acentos de uma string
        /// </summary>
        /// <param name="texto">Texto com acentos</param>
        /// <returns>Texto sem acentos</returns>
        public static string RemoverAcentos(string texto)
        {
            if (string.IsNullOrEmpty(texto))
                return string.Empty;

            var textoNormalizado = texto.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in textoNormalizado)
            {
                var categoria = CharUnicodeInfo.GetUnicodeCategory(c);
                if (categoria != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        /// <summary>
        /// Valida se uma string contém apenas dígitos
        /// </summary>
        /// <param name="texto">Texto para validar</param>
        /// <returns>True se contém apenas dígitos, False caso contrário</returns>
        public static bool EhApenasDigitos(string texto)
        {
            if (string.IsNullOrEmpty(texto))
                return false;

            return texto.All(char.IsDigit);
        }

        /// <summary>
        /// Valida se uma string é um CPF válido
        /// </summary>
        /// <param name="cpf">CPF para validar</param>
        /// <returns>True se é um CPF válido, False caso contrário</returns>
        public static bool ValidarCPF(string cpf)
        {
            if (string.IsNullOrEmpty(cpf))
                return false;

            // Remove caracteres não numéricos
            var cpfLimpo = new string(cpf.Where(char.IsDigit).ToArray());

            if (cpfLimpo.Length != 11)
                return false;

            // Verifica se todos os dígitos são iguais
            if (cpfLimpo.All(d => d == cpfLimpo[0]))
                return false;

            // Calcula o primeiro dígito verificador
            var soma = 0;
            for (int i = 0; i < 9; i++)
            {
                soma += int.Parse(cpfLimpo[i].ToString()) * (10 - i);
            }

            var digito1 = (soma * 10) % 11;
            if (digito1 == 10) digito1 = 0;

            if (digito1 != int.Parse(cpfLimpo[9].ToString()))
                return false;

            // Calcula o segundo dígito verificador
            soma = 0;
            for (int i = 0; i < 10; i++)
            {
                soma += int.Parse(cpfLimpo[i].ToString()) * (11 - i);
            }

            var digito2 = (soma * 10) % 11;
            if (digito2 == 10) digito2 = 0;

            return digito2 == int.Parse(cpfLimpo[10].ToString());
        }

        /// <summary>
        /// Valida se uma string é um CNPJ válido
        /// </summary>
        /// <param name="cnpj">CNPJ para validar</param>
        /// <returns>True se é um CNPJ válido, False caso contrário</returns>
        public static bool ValidarCNPJ(string cnpj)
        {
            if (string.IsNullOrEmpty(cnpj))
                return false;

            // Remove caracteres não numéricos
            var cnpjLimpo = new string(cnpj.Where(char.IsDigit).ToArray());

            if (cnpjLimpo.Length != 14)
                return false;

            // Verifica se todos os dígitos são iguais
            if (cnpjLimpo.All(d => d == cnpjLimpo[0]))
                return false;

            // Calcula o primeiro dígito verificador
            var pesos1 = new[] { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            var soma = 0;
            for (int i = 0; i < 12; i++)
            {
                soma += int.Parse(cnpjLimpo[i].ToString()) * pesos1[i];
            }

            var digito1 = soma % 11;
            if (digito1 < 2) digito1 = 0;
            else digito1 = 11 - digito1;

            if (digito1 != int.Parse(cnpjLimpo[12].ToString()))
                return false;

            // Calcula o segundo dígito verificador
            var pesos2 = new[] { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            soma = 0;
            for (int i = 0; i < 13; i++)
            {
                soma += int.Parse(cnpjLimpo[i].ToString()) * pesos2[i];
            }

            var digito2 = soma % 11;
            if (digito2 < 2) digito2 = 0;
            else digito2 = 11 - digito2;

            return digito2 == int.Parse(cnpjLimpo[13].ToString());
        }
    }
} 