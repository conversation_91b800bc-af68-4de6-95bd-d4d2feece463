using System;
using System.IO;

namespace SistemaMFT.Core.Entities
{
    /// <summary>
    /// Metadados de um arquivo
    /// Contém informações básicas sobre arquivos processados
    /// Usado para validações e controle de processamento
    /// </summary>
    public class FileMetadata
    {
        /// <summary>
        /// Nome do arquivo sem o caminho
        /// </summary>
        public string Nome { get; set; } = string.Empty;

        /// <summary>
        /// Caminho completo do arquivo
        /// </summary>
        public string CaminhoCompleto { get; set; } = string.Empty;

        /// <summary>
        /// Extensão do arquivo
        /// </summary>
        public string Extensao { get; set; } = string.Empty;

        /// <summary>
        /// Tamanho do arquivo em bytes
        /// </summary>
        public long TamanhoBytes { get; set; }

        /// <summary>
        /// Data de criação do arquivo
        /// </summary>
        public DateTime DataCriacao { get; set; }

        /// <summary>
        /// Data da última modificação
        /// </summary>
        public DateTime DataModificacao { get; set; }

        /// <summary>
        /// Indica se o arquivo existe
        /// </summary>
        public bool Existe { get; set; }

        /// <summary>
        /// Cria metadados a partir de um FileInfo
        /// </summary>
        /// <param name="fileInfo">Informações do arquivo</param>
        /// <returns>Metadados do arquivo</returns>
        public static FileMetadata CriarDeFileInfo(FileInfo fileInfo)
        {
            return new FileMetadata
            {
                Nome = fileInfo.Name,
                CaminhoCompleto = fileInfo.FullName,
                Extensao = fileInfo.Extension,
                TamanhoBytes = fileInfo.Exists ? fileInfo.Length : 0,
                DataCriacao = fileInfo.Exists ? fileInfo.CreationTime : DateTime.MinValue,
                DataModificacao = fileInfo.Exists ? fileInfo.LastWriteTime : DateTime.MinValue,
                Existe = fileInfo.Exists
            };
        }

        /// <summary>
        /// Obtém o tamanho formatado do arquivo
        /// </summary>
        /// <returns>Tamanho formatado (KB, MB, etc.)</returns>
        public string ObterTamanhoFormatado()
        {
            if (TamanhoBytes < 1024)
                return $"{TamanhoBytes} bytes";
            
            if (TamanhoBytes < 1024 * 1024)
                return $"{TamanhoBytes / 1024.0:F1} KB";
            
            if (TamanhoBytes < 1024 * 1024 * 1024)
                return $"{TamanhoBytes / (1024.0 * 1024.0):F1} MB";
            
            return $"{TamanhoBytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }
    }

    /// <summary>
    /// Informações sobre um tipo de arquivo suportado
    /// Usado para identificação automática de processadores
    /// </summary>
    public class FileTypeInfo
    {
        /// <summary>
        /// Nome do tipo de arquivo (ex: "CONTRATOS", "TELEFONES")
        /// </summary>
        public string TipoArquivo { get; set; } = string.Empty;

        /// <summary>
        /// Código identificador do registro (ex: "200", "701")
        /// </summary>
        public string CodigoRegistro { get; set; } = string.Empty;

        /// <summary>
        /// Padrão do nome do arquivo (ex: "*CONTRATOS_OUT.txt")
        /// </summary>
        public string PadraoNome { get; set; } = string.Empty;

        /// <summary>
        /// Tamanho esperado de cada linha em caracteres
        /// </summary>
        public int TamanhoLinha { get; set; }

        /// <summary>
        /// Extensões de arquivo suportadas
        /// </summary>
        public string[] ExtensoesSuportadas { get; set; } = new[] { ".txt" };

        /// <summary>
        /// Descrição do tipo de arquivo
        /// </summary>
        public string Descricao { get; set; } = string.Empty;

        /// <summary>
        /// Verifica se um arquivo corresponde a este tipo
        /// </summary>
        /// <param name="nomeArquivo">Nome do arquivo a verificar</param>
        /// <returns>True se o arquivo corresponde a este tipo</returns>
        public bool CorrespondeAoTipo(string nomeArquivo)
        {
            if (string.IsNullOrWhiteSpace(nomeArquivo))
                return false;

            var nome = Path.GetFileName(nomeArquivo).ToUpperInvariant();
            var extensao = Path.GetExtension(nomeArquivo).ToLowerInvariant();

            // Verifica extensão
            var extensaoValida = Array.Exists(ExtensoesSuportadas, ext => ext.Equals(extensao, StringComparison.OrdinalIgnoreCase));
            if (!extensaoValida)
                return false;

            // Verifica padrão do nome
            if (!string.IsNullOrWhiteSpace(PadraoNome))
            {
                var padrao = PadraoNome.ToUpperInvariant().Replace("*", "");
                return nome.Contains(padrao);
            }

            return true;
        }
    }
}
