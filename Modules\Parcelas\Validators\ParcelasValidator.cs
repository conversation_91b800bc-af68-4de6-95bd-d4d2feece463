using System;
using System.Collections.Generic;
using SistemaMFT.Core.Interfaces;
using SistemaMFT.Core.Entities;
using SistemaMFT.Core.Common;
using SistemaMFT.Modules.Parcelas.Entities;

namespace SistemaMFT.Modules.Parcelas.Validators
{
    /// <summary>
    /// Validador específico para registros de parcelas
    /// Implementa IValidator para fornecer validação completa dos dados de parcela
    /// Valida campos obrigatórios, formatos, valores permitidos e consistência de dados
    /// </summary>
    public class ParcelasValidator : IValidator<RegistroParcela>
    {
        /// <summary>
        /// Valida um registro completo de parcela
        /// </summary>
        /// <param name="parcela">Registro de parcela a ser validado</param>
        /// <returns>Resultado da validação com lista de erros se houver</returns>
        public ValidationResult Validar(RegistroParcela parcela)
        {
            var resultado = new ValidationResult();

            if (parcela == null)
            {
                resultado.Erros.Add("Registro de parcela não pode ser nulo");
                return resultado;
            }

            // Validação do tipo de registro
            ValidarTipoRegistro(parcela.TipoRegistro, resultado);

            // Validação do grupo
            ValidarGrupo(parcela.Grupo, resultado);

            // Validação do número do contrato
            ValidarNumeroContrato(parcela.NumeroContrato, resultado);

            // Validação do número da parcela
            ValidarNumeroParcela(parcela.NumeroParcela, resultado);

            // Validação da data de vencimento
            ValidarDataVencimento(parcela.DataVencimento, resultado);

            // Validação do valor principal
            ValidarValorPrincipal(parcela.ValorPrincipal, resultado);

            // Validação do valor de juros (opcional)
            ValidarValorJuros(parcela.ValorJuros, resultado);

            // Validação do valor de multa (opcional)
            ValidarValorMulta(parcela.ValorMulta, resultado);

            // Validação do status da parcela
            ValidarStatusParcela(parcela.StatusParcela, resultado);

            // Validação da data de pagamento (opcional)
            ValidarDataPagamento(parcela.DataPagamento, resultado);

            // Validação do valor pago (opcional)
            ValidarValorPago(parcela.ValorPago, resultado);

            // Validação do ranking (opcional)
            ValidarRanking(parcela.Ranking, resultado);

            // Validações de consistência
            ValidarConsistenciaStatus(parcela, resultado);

            return resultado;
        }

        /// <summary>
        /// Valida um campo específico da parcela
        /// </summary>
        /// <param name="valor">Valor do campo</param>
        /// <param name="nomeCampo">Nome do campo para identificação</param>
        /// <returns>Resultado da validação do campo</returns>
        public FieldValidationResult ValidarCampo(string valor, string nomeCampo)
        {
            var resultado = new FieldValidationResult { IsValid = true };

            switch (nomeCampo.ToLower())
            {
                case "tiporegistro":
                    if (string.IsNullOrWhiteSpace(valor) || valor.Trim() != "000")
                    {
                        resultado.IsValid = false;
                        resultado.Erro = $"Tipo de registro deve ser '000', encontrado: '{valor}'";
                    }
                    break;
                case "grupo":
                    if (!ValidationHelper.ValidarValoresPermitidos(valor?.Trim(), "1", "2"))
                    {
                        resultado.IsValid = false;
                        resultado.Erro = $"Grupo deve ser '1' ou '2', encontrado: '{valor}'";
                    }
                    break;
                case "numerocontrato":
                    if (string.IsNullOrWhiteSpace(valor))
                    {
                        resultado.IsValid = false;
                        resultado.Erro = "Número do contrato é obrigatório";
                    }
                    break;
                case "numeroparcela":
                    if (!int.TryParse(valor?.Trim(), out int numero) || numero <= 0)
                    {
                        resultado.IsValid = false;
                        resultado.Erro = $"Número da parcela deve ser um número positivo, encontrado: '{valor}'";
                    }
                    break;
                case "datavencimento":
                    var validacaoData = ValidationHelper.ValidarDataMMDDAAAA(valor);
                    if (!validacaoData.valido)
                    {
                        resultado.IsValid = false;
                        resultado.Erro = $"Data de vencimento inválida: {validacaoData.erro}";
                    }
                    break;
                case "valorprincipal":
                    var validacaoValor = ValidationHelper.ValidarValorMonetario(valor, 2);
                    if (!validacaoValor.valido || (validacaoValor.valorFormatado.HasValue && validacaoValor.valorFormatado.Value <= 0))
                    {
                        resultado.IsValid = false;
                        resultado.Erro = $"Valor principal deve ser maior que zero, encontrado: '{valor}'";
                    }
                    break;
                case "valorjuros":
                case "valormulta":
                case "valorpago":
                    if (!string.IsNullOrWhiteSpace(valor))
                    {
                        var validacaoValorOpcional = ValidationHelper.ValidarValorMonetario(valor, 2);
                        if (!validacaoValorOpcional.valido)
                        {
                            resultado.IsValid = false;
                            resultado.Erro = $"Valor inválido: {validacaoValorOpcional.erro}";
                        }
                    }
                    break;
                case "statusparcela":
                    var validacaoStatus = ValidationHelper.ValidarStatus(valor, "1", "2", "3");
                    if (!validacaoStatus.valido)
                    {
                        resultado.IsValid = false;
                        resultado.Erro = $"Status inválido: {validacaoStatus.erro}";
                    }
                    break;
                case "datapagamento":
                    if (!string.IsNullOrWhiteSpace(valor) && valor.Trim() != "00000000")
                    {
                        var validacaoDataPag = ValidationHelper.ValidarDataMMDDAAAA(valor);
                        if (!validacaoDataPag.valido)
                        {
                            resultado.IsValid = false;
                            resultado.Erro = $"Data de pagamento inválida: {validacaoDataPag.erro}";
                        }
                    }
                    break;
                case "ranking":
                    if (!string.IsNullOrWhiteSpace(valor))
                    {
                        var validacaoRanking = ValidationHelper.ValidarRanking(valor);
                        if (!validacaoRanking.valido)
                        {
                            resultado.IsValid = false;
                            resultado.Erro = $"Ranking inválido: {validacaoRanking.erro}";
                        }
                    }
                    break;
                default:
                    resultado.IsValid = false;
                    resultado.Erro = $"Campo '{nomeCampo}' não reconhecido para validação";
                    break;
            }

            return resultado;
        }

        /// <summary>
        /// Obtém as regras de validação para parcelas
        /// </summary>
        /// <returns>Lista de regras de validação</returns>
        public IEnumerable<ValidationRule> ObterRegrasValidacao()
        {
            return new List<ValidationRule>
            {
                new ValidationRule { Campo = "TipoRegistro", Regra = "Valor fixo", Descricao = "Deve ser sempre '000'" },
                new ValidationRule { Campo = "Grupo", Regra = "Valores permitidos", Descricao = "'1' para QuintoAndar ou '2' para QuintoCred" },
                new ValidationRule { Campo = "NumeroContrato", Regra = "Obrigatório", Descricao = "Até 25 caracteres" },
                new ValidationRule { Campo = "NumeroParcela", Regra = "Obrigatório", Descricao = "Número inteiro positivo até 3 dígitos" },
                new ValidationRule { Campo = "DataVencimento", Regra = "Obrigatório", Descricao = "Formato MMDDAAAA" },
                new ValidationRule { Campo = "ValorPrincipal", Regra = "Obrigatório", Descricao = "Valor monetário maior que zero" },
                new ValidationRule { Campo = "ValorJuros", Regra = "Opcional", Descricao = "Valor monetário com 2 decimais implícitas" },
                new ValidationRule { Campo = "ValorMulta", Regra = "Opcional", Descricao = "Valor monetário com 2 decimais implícitas" },
                new ValidationRule { Campo = "StatusParcela", Regra = "Valores permitidos", Descricao = "'1' (Aberto), '2' (Pago) ou '3' (Cancelado)" },
                new ValidationRule { Campo = "DataPagamento", Regra = "Opcional", Descricao = "Formato MMDDAAAA ou 00000000" },
                new ValidationRule { Campo = "ValorPago", Regra = "Opcional", Descricao = "Valor monetário com 2 decimais implícitas" },
                new ValidationRule { Campo = "Ranking", Regra = "Opcional", Descricao = "Número de 1 a 9999" },
                new ValidationRule { Campo = "Consistência", Regra = "Status Pago", Descricao = "Se status for 'Pago', deve ter data de pagamento" },
                new ValidationRule { Campo = "Consistência", Regra = "Data/Valor", Descricao = "Se tem data de pagamento, deve ter valor pago" }
            };
        }

        #region Métodos de Validação Específicos

        private void ValidarTipoRegistro(string tipoRegistro, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(tipoRegistro))
            {
                resultado.Erros.Add("Tipo de registro é obrigatório");
                return;
            }

            if (tipoRegistro.Trim() != "000")
            {
                resultado.Erros.Add($"Tipo de registro deve ser '000', encontrado: '{tipoRegistro}'");
            }
        }

        private void ValidarGrupo(string grupo, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(grupo))
            {
                resultado.Erros.Add("Grupo é obrigatório");
                return;
            }

            if (!ValidationHelper.ValidarValoresPermitidos(grupo.Trim(), "1", "2"))
            {
                resultado.Erros.Add($"Grupo deve ser '1' (QuintoAndar) ou '2' (QuintoCred), encontrado: '{grupo}'");
            }
        }

        private void ValidarNumeroContrato(string numeroContrato, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(numeroContrato))
            {
                resultado.Erros.Add("Número do contrato é obrigatório");
                return;
            }

            if (numeroContrato.Length > 25)
            {
                resultado.Erros.Add($"Número do contrato não pode ter mais de 25 caracteres, encontrado: {numeroContrato.Length}");
            }
        }

        private void ValidarNumeroParcela(string numeroParcela, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(numeroParcela))
            {
                resultado.Erros.Add("Número da parcela é obrigatório");
                return;
            }

            if (!int.TryParse(numeroParcela.Trim(), out int numero) || numero <= 0)
            {
                resultado.Erros.Add($"Número da parcela deve ser um número inteiro positivo, encontrado: '{numeroParcela}'");
            }
        }

        private void ValidarDataVencimento(string dataVencimento, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(dataVencimento))
            {
                resultado.Erros.Add("Data de vencimento é obrigatória");
                return;
            }

            var validacao = ValidationHelper.ValidarDataMMDDAAAA(dataVencimento);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Data de vencimento inválida: {validacao.erro}");
            }
        }

        private void ValidarValorPrincipal(string valorPrincipal, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(valorPrincipal))
            {
                resultado.Erros.Add("Valor principal é obrigatório");
                return;
            }

            var validacao = ValidationHelper.ValidarValorMonetario(valorPrincipal, 2);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Valor principal inválido: {validacao.erro}");
            }
            else if (validacao.valorFormatado.HasValue && validacao.valorFormatado.Value <= 0)
            {
                resultado.Erros.Add("Valor principal deve ser maior que zero");
            }
        }

        private void ValidarValorJuros(string valorJuros, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(valorJuros)) return; // Campo opcional

            var validacao = ValidationHelper.ValidarValorMonetario(valorJuros, 2);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Valor de juros inválido: {validacao.erro}");
            }
        }

        private void ValidarValorMulta(string valorMulta, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(valorMulta)) return; // Campo opcional

            var validacao = ValidationHelper.ValidarValorMonetario(valorMulta, 2);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Valor de multa inválido: {validacao.erro}");
            }
        }

        private void ValidarStatusParcela(string statusParcela, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(statusParcela))
            {
                resultado.Erros.Add("Status da parcela é obrigatório");
                return;
            }

            var validacao = ValidationHelper.ValidarStatus(statusParcela, "1", "2", "3");
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Status da parcela inválido: {validacao.erro}");
            }
        }

        private void ValidarDataPagamento(string dataPagamento, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(dataPagamento)) return; // Campo opcional
            if (dataPagamento.Trim() == "00000000") return; // Data nula válida

            var validacao = ValidationHelper.ValidarDataMMDDAAAA(dataPagamento);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Data de pagamento inválida: {validacao.erro}");
            }
        }

        private void ValidarValorPago(string valorPago, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(valorPago)) return; // Campo opcional

            var validacao = ValidationHelper.ValidarValorMonetario(valorPago, 2);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Valor pago inválido: {validacao.erro}");
            }
        }

        private void ValidarRanking(string ranking, ValidationResult resultado)
        {
            if (string.IsNullOrWhiteSpace(ranking)) return; // Campo opcional

            var validacao = ValidationHelper.ValidarRanking(ranking);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Ranking inválido: {validacao.erro}");
            }
        }

        private void ValidarConsistenciaStatus(RegistroParcela parcela, ValidationResult resultado)
        {
            // Se status é "Pago" (2), deve ter data de pagamento
            if (parcela.StatusParcela == "2")
            {
                if (string.IsNullOrWhiteSpace(parcela.DataPagamento) || parcela.DataPagamento.Trim() == "00000000")
                {
                    resultado.Erros.Add("Parcela marcada como 'Pago' deve ter data de pagamento");
                }
            }

            // Se tem data de pagamento válida, deve ter valor pago
            if (!string.IsNullOrWhiteSpace(parcela.DataPagamento) && 
                parcela.DataPagamento.Trim() != "00000000")
            {
                if (string.IsNullOrWhiteSpace(parcela.ValorPago))
                {
                    resultado.Erros.Add("Parcela com data de pagamento deve ter valor pago informado");
                }
            }
        }

        #endregion
    }
}
