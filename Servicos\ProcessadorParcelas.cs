using System;
using System.Collections.Generic;
using System.Linq;
using SistemaMFT.Entidades;
using SistemaMFT.Utilitarios;

namespace SistemaMFT.Servicos
{
    /// <summary>
    /// Processador para arquivos de parcelas (PARCELAS_OUT.txt)
    /// Layout: 106 caracteres por linha
    /// Código de registro: "000"
    /// </summary>
    public static class ProcessadorParcelas
    {
        /// <summary>
        /// Processa uma linha do arquivo de parcelas
        /// </summary>
        /// <param name="linha">Linha a ser processada</param>
        /// <returns>Tupla com resultado da validação e registro processado</returns>
        public static (bool IsValid, RegistroParcelaNova Registro, string[] Erros) ProcessarLinhaParcela(string linha)
        {
            var erros = new List<string>();
            var registro = new RegistroParcelaNova();

            // Verifica se a linha não está vazia
            if (string.IsNullOrEmpty(linha))
            {
                erros.Add("Linha está vazia");
                return (false, registro, erros.ToArray());
            }

            // Se a linha tem exatamente 106 caracteres, processa no formato original
            if (linha.Length == 106)
            {
                return ProcessarLinhaFormatoOriginal(linha);
            }

            // Se a linha é muito longa, pode ser um formato diferente
            if (linha.Length > 106)
            {
                Console.WriteLine($"AVISO: Linha com {linha.Length} caracteres detectada. Tentando processar como formato alternativo...");

                // Tenta processar como formato delimitado ou múltiplas parcelas
                return ProcessarLinhaFormatoAlternativo(linha);
            }

            // Se a linha é muito curta
            erros.Add($"Linha muito curta. Esperado: 106 caracteres, atual: {linha.Length}");
            return (false, registro, erros.ToArray());
        }

        /// <summary>
        /// Processa linha no formato original de 106 caracteres
        /// </summary>
        private static (bool IsValid, RegistroParcelaNova Registro, string[] Erros) ProcessarLinhaFormatoOriginal(string linha)
        {
            var erros = new List<string>();
            var registro = new RegistroParcelaNova();

            try
            {
                // Extração dos campos conforme layout
                registro.TipoRegistro = linha.Substring(0, 3).Trim(); // Posições 1-3
                registro.Grupo = linha.Substring(3, 1).Trim(); // Posição 4
                registro.ChaveCyber = linha.Substring(4, 25).Trim(); // Posições 5-29
                registro.IdParcela = linha.Substring(29, 15).Trim(); // Posições 30-44
                registro.StatusParcela = linha.Substring(44, 1).Trim(); // Posição 45
                registro.ValorPrincipal = linha.Substring(45, 15).Trim(); // Posições 46-60
                registro.ValorJuros = linha.Substring(60, 15).Trim(); // Posições 61-75
                registro.ValorMulta = linha.Substring(75, 15).Trim(); // Posições 76-90
                registro.DataVencimento = linha.Substring(90, 8).Trim(); // Posições 91-98
                registro.DataPagamento = linha.Substring(98, 8).Trim(); // Posições 99-106

                return ProcessarValidacoesParcela(registro, erros);
            }
            catch (Exception ex)
            {
                erros.Add($"Erro ao extrair campos: {ex.Message}");
                return (false, registro, erros.ToArray());
            }
        }

        /// <summary>
        /// Processa linha em formato alternativo (linhas longas)
        /// </summary>
        private static (bool IsValid, RegistroParcelaNova Registro, string[] Erros) ProcessarLinhaFormatoAlternativo(string linha)
        {
            var erros = new List<string>();
            var registro = new RegistroParcelaNova();

            try
            {
                // Tenta extrair apenas os primeiros 106 caracteres
                if (linha.Length >= 106)
                {
                    var linhaTruncada = linha.Substring(0, 106);
                    Console.WriteLine($"AVISO: Truncando linha de {linha.Length} para 106 caracteres");
                    return ProcessarLinhaFormatoOriginal(linhaTruncada);
                }

                erros.Add($"Não foi possível processar linha com {linha.Length} caracteres");
                return (false, registro, erros.ToArray());
            }
            catch (Exception ex)
            {
                erros.Add($"Erro ao processar formato alternativo: {ex.Message}");
                return (false, registro, erros.ToArray());
            }
        }

        /// <summary>
        /// Executa as validações e logging para uma parcela
        /// </summary>
        private static (bool IsValid, RegistroParcelaNova Registro, string[] Erros) ProcessarValidacoesParcela(RegistroParcelaNova registro, List<string> erros)
        {
            try
            {

                // Log dos campos extraídos
                Console.WriteLine($"PRESTACAO_FINANCEIRA.TIPO_REGISTRO: {registro.TipoRegistro}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.GRUPO: {registro.Grupo}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.CHAVE_CYBER: {registro.ChaveCyber}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.ID_PARCELA: {registro.IdParcela}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.STATUS_PARCELA: {registro.StatusParcela}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.VALOR_PRINCIPAL: {registro.ValorPrincipal}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.VALOR_JUROS: {registro.ValorJuros}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.VALOR_MULTA: {registro.ValorMulta}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.DATA_VENCIMENTO: {registro.DataVencimento}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.DATA_PAGAMENTO: {registro.DataPagamento}");

                // Campos calculados
                var valorTotal = registro.ValorTotal;
                var diasAtraso = registro.DiasAtraso;
                var situacaoAtraso = registro.SituacaoAtraso;

                Console.WriteLine($"PRESTACAO_FINANCEIRA.VALOR_TOTAL: {valorTotal:F2}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.DIAS_ATRASO: {diasAtraso}");
                Console.WriteLine($"PRESTACAO_FINANCEIRA.SITUACAO_ATRASO: {situacaoAtraso}");

                // Validações
                ValidarTipoRegistro(registro.TipoRegistro, erros);
                ValidarGrupo(registro.Grupo, erros);
                ValidarChaveCyber(registro.ChaveCyber, erros);
                ValidarIdParcela(registro.IdParcela, erros);
                ValidarStatusParcela(registro.StatusParcela, erros);
                ValidarValorMonetario(registro.ValorPrincipal, "Valor Principal", erros);
                ValidarValorMonetario(registro.ValorJuros, "Valor Juros", erros);
                ValidarValorMonetario(registro.ValorMulta, "Valor Multa", erros);
                ValidarDataVencimento(registro.DataVencimento, erros);
                ValidarDataPagamento(registro.DataPagamento, registro.StatusParcela, erros);

                return (erros.Count == 0, registro, erros.ToArray());
            }
            catch (Exception ex)
            {
                erros.Add($"Erro ao processar linha: {ex.Message}");
                return (false, registro, erros.ToArray());
            }
        }

        private static void ValidarTipoRegistro(string tipoRegistro, List<string> erros)
        {
            if (tipoRegistro != "000")
            {
                erros.Add($"Tipo de registro deve ser '000'. Valor encontrado: '{tipoRegistro}'");
            }
        }

        private static void ValidarGrupo(string grupo, List<string> erros)
        {
            var gruposPermitidos = new[] { "1", "2" };
            var resultado = ValidadoresUniversais.ValidarStatus(grupo, gruposPermitidos, new Dictionary<string, string>
            {
                { "1", "QuintoAndar - Locação" },
                { "2", "QuintoCred - Crédito" }
            });

            if (!resultado.valido)
            {
                erros.Add($"Grupo inválido: {resultado.erro}");
            }
        }

        private static void ValidarChaveCyber(string chaveCyber, List<string> erros)
        {
            if (string.IsNullOrWhiteSpace(chaveCyber))
            {
                erros.Add("Chave Cyber não pode estar vazia");
            }
        }

        private static void ValidarIdParcela(string idParcela, List<string> erros)
        {
            if (string.IsNullOrWhiteSpace(idParcela))
            {
                erros.Add("ID da parcela não pode estar vazio");
            }
        }

        private static void ValidarStatusParcela(string statusParcela, List<string> erros)
        {
            var statusPermitidos = new[] { "1", "2", "3", "4", "5", "6" };
            var descricoes = new Dictionary<string, string>
            {
                { "1", "Ativa (em aberto)" },
                { "2", "Paga" },
                { "3", "Cancelada" },
                { "4", "Pausa" },
                { "5", "Previamente Negociada" },
                { "6", "Em Negociação" }
            };

            var resultado = ValidadoresUniversais.ValidarStatus(statusParcela, statusPermitidos, descricoes);
            if (!resultado.valido)
            {
                erros.Add($"Status da parcela inválido: {resultado.erro}");
            }
        }

        private static void ValidarValorMonetario(string valor, string nomeValor, List<string> erros)
        {
            var resultado = ValidadoresUniversais.ValidarValorMonetario(valor);
            if (!resultado.valido)
            {
                erros.Add($"{nomeValor} inválido: {resultado.erro}");
            }
        }

        private static void ValidarDataVencimento(string dataVencimento, List<string> erros)
        {
            var resultado = ValidadoresUniversais.ValidarDataMMDDAAAA(dataVencimento);
            if (!resultado.valido)
            {
                erros.Add($"Data de vencimento inválida: {resultado.erro}");
            }
        }

        private static void ValidarDataPagamento(string dataPagamento, string statusParcela, List<string> erros)
        {
            // Data de pagamento é obrigatória apenas para parcelas pagas (status "2")
            if (statusParcela == "2")
            {
                if (string.IsNullOrWhiteSpace(dataPagamento))
                {
                    erros.Add("Data de pagamento é obrigatória para parcelas pagas");
                    return;
                }

                var resultado = ValidadoresUniversais.ValidarDataMMDDAAAA(dataPagamento);
                if (!resultado.valido)
                {
                    erros.Add($"Data de pagamento inválida: {resultado.erro}");
                }
            }
            else if (!string.IsNullOrWhiteSpace(dataPagamento))
            {
                // Se há data de pagamento, deve ser válida
                var resultado = ValidadoresUniversais.ValidarDataMMDDAAAA(dataPagamento);
                if (!resultado.valido)
                {
                    erros.Add($"Data de pagamento inválida: {resultado.erro}");
                }
            }
        }
    }
}
