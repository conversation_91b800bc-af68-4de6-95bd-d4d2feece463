using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa o trailer padrão de todos os arquivos MFT
    /// Estrutura: Posição 1 = "T", Posições 2-9 = Data geração, Posições 10-17 = Qtd registros
    /// </summary>
    public class TrailerArquivo
    {
        /// <summary>
        /// Identificador do trailer - sempre "T" na posição 1
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Identificador { get; set; } = "T";

        /// <summary>
        /// Data de geração do arquivo - posições 2-9 (formato DDMMYYYY)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataGeracao { get; set; } = string.Empty;

        /// <summary>
        /// Quantidade total de registros no arquivo - posições 10-17
        /// </summary>
        [Required]
        [StringLength(8)]
        public string QuantidadeRegistros { get; set; } = string.Empty;

        /// <summary>
        /// Converte a data de geração (string DDMMYYYY) para DateTime
        /// </summary>
        /// <returns>Data de geração convertida para DateTime</returns>
        public DateTime ObterDataGeracao()
        {
            if (string.IsNullOrEmpty(DataGeracao) || DataGeracao.Length != 8)
            {
                throw new ArgumentException("Data de geração deve ter 8 dígitos no formato DDMMYYYY");
            }

            var dia = int.Parse(DataGeracao.Substring(0, 2));
            var mes = int.Parse(DataGeracao.Substring(2, 2));
            var ano = int.Parse(DataGeracao.Substring(4, 4));

            return new DateTime(ano, mes, dia);
        }

        /// <summary>
        /// Define a data de geração a partir de um DateTime
        /// </summary>
        /// <param name="data">Data para converter</param>
        public void DefinirDataGeracao(DateTime data)
        {
            DataGeracao = data.ToString("ddMMyyyy");
        }

        /// <summary>
        /// Obtém a quantidade de registros como inteiro
        /// </summary>
        /// <returns>Quantidade de registros convertida para int</returns>
        public int ObterQuantidadeRegistros()
        {
            if (string.IsNullOrEmpty(QuantidadeRegistros))
            {
                throw new ArgumentException("Quantidade de registros não pode ser vazia");
            }

            return int.Parse(QuantidadeRegistros.Trim());
        }

        /// <summary>
        /// Define a quantidade de registros a partir de um inteiro
        /// </summary>
        /// <param name="quantidade">Quantidade de registros</param>
        public void DefinirQuantidadeRegistros(int quantidade)
        {
            QuantidadeRegistros = quantidade.ToString().PadLeft(8, '0');
        }

        /// <summary>
        /// Valida se o trailer está correto
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarTrailer()
        {
            return Identificador == "T" &&
                   !string.IsNullOrEmpty(DataGeracao) &&
                   DataGeracao.Length == 8 &&
                   !string.IsNullOrEmpty(QuantidadeRegistros) &&
                   int.TryParse(QuantidadeRegistros.Trim(), out _);
        }
    }
} 