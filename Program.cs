using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SistemaMFT.Configuracao;
using SistemaMFT.Core.Interfaces;
using SistemaMFT.Core.Entities;
using SistemaMFT.Modules.Contratos.Entities;
using SistemaMFT.Modules.Contratos.Processors;
using SistemaMFT.Modules.Contratos.Services;
using SistemaMFT.Modules.Telefones.Entities;
using SistemaMFT.Modules.Telefones.Processors;
using SistemaMFT.Modules.Telefones.Services;
using SistemaMFT.Modules.Parcelas.Entities;
using SistemaMFT.Modules.Parcelas.Processors;
using SistemaMFT.Modules.Parcelas.Services;
using SistemaMFT.Servicos;
using PgpCore;
using System.IO.Compression;
using System.Text;

namespace SistemaMFT
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.OutputEncoding = Encoding.UTF8;

            Console.WriteLine("=== SISTEMA DE AUTOMAÇÃO DE EXTRAÇÃO DE DADOS - QUINTOANDAR ===");
            Console.WriteLine();
            Console.WriteLine("Tipos de arquivo suportados:");
            Console.WriteLine("- CONTRATOS_OUT.txt (código 200) - Layout 155 caracteres");
            Console.WriteLine("- TELEFONES_OUT.txt (código 701) - Layout 77 caracteres");
            Console.WriteLine("- PARCELAS_OUT.txt (código 000) - Layout 106 caracteres");
            Console.WriteLine();

            var serviceProvider = ConfigurarServicos();
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            var configuracao = serviceProvider.GetRequiredService<ConfiguracaoMFT>();
            var servicoSFTP = serviceProvider.GetRequiredService<ServicoSFTP>();

            // Novos processadores modulares
            var processadorContratos = serviceProvider.GetRequiredService<IFileProcessor<RegistroContrato>>();
            var processadorTelefones = serviceProvider.GetRequiredService<IFileProcessor<RegistroTelefone>>();
            var processadorParcelas = serviceProvider.GetRequiredService<IFileProcessor<RegistroParcela>>();

            try
            {
                Console.WriteLine("=== SISTEMA MFT - FLUXO AUTOMÁTICO ===\n");
                Console.WriteLine("💡 Para executar exemplo: dotnet run exemplo\n");
                configuracao.ConfiguracaoArquivos.CriarDiretorios();

                // 1. Conectar e baixar arquivos .zip.gpg
                var arquivosBaixados = await servicoSFTP.BaixarArquivosAsync();
                if (!arquivosBaixados.Any())
                {
                    Console.WriteLine("Nenhum arquivo .zip.gpg encontrado no servidor.");
                    return;
                }

                // 2. Descriptografar e descompactar cada arquivo
                foreach (var arquivoGpg in arquivosBaixados)
                {
                    try
                    {
                        Console.WriteLine($"\n🔓 Descriptografando: {Path.GetFileName(arquivoGpg)}");
                        var arquivoZip = await DescriptografarArquivoGpgAsync(arquivoGpg, configuracao);
                        Console.WriteLine($"✅ Descriptografado para: {arquivoZip}");

                        Console.WriteLine($"📦 Descompactando: {Path.GetFileName(arquivoZip)}");
                        var arquivosTxt = DescompactarZip(arquivoZip, configuracao.ConfiguracaoArquivos.DiretorioEntrada);
                        foreach (var txt in arquivosTxt)
                        {
                            Console.WriteLine($"\n📄 Processando TXT: {Path.GetFileName(txt)}");
                            await ProcessarEExibirEntidadeAsync(txt, processadorContratos, processadorTelefones, processadorParcelas);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"❌ Erro ao processar {arquivoGpg}: {ex.Message}");
                        Console.ResetColor();
                        logger.LogError(ex, "Erro no processamento automático");
                    }
                }

                Console.WriteLine("\n🎉 Fluxo automático concluído!");
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"ERRO CRÍTICO: {ex.Message}");
                Console.ResetColor();
                logger.LogCritical(ex, "Erro crítico no fluxo automático");
            }
        }

        private static ServiceProvider ConfigurarServicos()
        {
            var configuracao = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            var services = new ServiceCollection();
            services.AddLogging(builder =>
            {
                builder.AddConfiguration(configuracao.GetSection("Logging"));
                builder.AddConsole();
            });

            // Configurações
            var configuracaoMFT = new ConfiguracaoMFT();
            configuracao.GetSection("ConfiguracaoMFT").Bind(configuracaoMFT);
            services.AddSingleton(configuracaoMFT);
            var configuracaoAPI = new ConfiguracaoAPI();
            configuracao.GetSection("ConfiguracaoAPI").Bind(configuracaoAPI);
            services.AddSingleton(configuracaoAPI);

            // Serviços da estrutura antiga (mantidos para compatibilidade)
            services.AddTransient<ServicoSFTP>();

            // Novos processadores modulares
            services.AddTransient<IFileProcessor<RegistroContrato>, ContratosProcessor>();
            services.AddTransient<IFileProcessor<RegistroTelefone>, TelefonesProcessor>();
            services.AddTransient<IFileProcessor<RegistroParcela>, ParcelasProcessor>();

            // Serviços de alto nível
            services.AddTransient<ContratosService>();
            services.AddTransient<TelefonesService>();
            services.AddTransient<ParcelasService>();

            return services.BuildServiceProvider();
        }

        private static async Task<string> DescriptografarArquivoGpgAsync(string arquivoGpg, ConfiguracaoMFT cfg)
        {
            // Salvar o arquivo descriptografado na mesma pasta do arquivoGpg, trocando a extensão
            var pastaDestino = Path.GetDirectoryName(arquivoGpg);
            var nomeZip = Path.GetFileNameWithoutExtension(arquivoGpg) + ".zip";
            var arquivoZip = Path.Combine(pastaDestino, nomeZip);
            var keys = new EncryptionKeys(
                new FileInfo(cfg.ConfiguracaoGPG.CaminhoChavePrivada),
                cfg.ConfiguracaoGPG.SenhaChavePrivada
            );
            var pgp = new PGP(keys);
            await pgp.DecryptAsync(
                new FileInfo(arquivoGpg),
                new FileInfo(arquivoZip)
            );
            return arquivoZip;
        }

        private static List<string> DescompactarZip(string arquivoZip, string destino)
        {
            // Extrair para a mesma pasta do arquivoZip
            var pastaDestino = Path.GetDirectoryName(arquivoZip);
            var arquivosExtraidos = new List<string>();
            using (var zip = ZipFile.OpenRead(arquivoZip))
            {
                foreach (var entry in zip.Entries)
                {
                    if (entry.FullName.EndsWith(".txt", StringComparison.OrdinalIgnoreCase))
                    {
                        var caminhoDestino = Path.Combine(pastaDestino, entry.FullName);
                        entry.ExtractToFile(caminhoDestino, overwrite: true);
                        arquivosExtraidos.Add(caminhoDestino);
                    }
                }
            }
            return arquivosExtraidos;
        }

        private static async Task ProcessarEExibirEntidadeAsync(
            string caminhoTxt,
            IFileProcessor<RegistroContrato> processadorContratos,
            IFileProcessor<RegistroTelefone> processadorTelefones,
            IFileProcessor<RegistroParcela> processadorParcelas)
        {
            var nome = Path.GetFileName(caminhoTxt).ToUpperInvariant();

            if (nome.Contains("CONTRATO"))
            {
                Console.WriteLine("🔍 Detectado arquivo de CONTRATOS");
                var resultado = await processadorContratos.ProcessarArquivoAsync(caminhoTxt);
                ExibirResultadoProcessamentoModular(resultado, "CONTRATOS");
            }
            else if (nome.Contains("TELEFONE"))
            {
                Console.WriteLine("🔍 Detectado arquivo de TELEFONES");
                var resultado = await processadorTelefones.ProcessarArquivoAsync(caminhoTxt);
                ExibirResultadoProcessamentoModular(resultado, "TELEFONES");
            }
            else if (nome.Contains("PARCELA"))
            {
                Console.WriteLine("🔍 Detectado arquivo de PARCELAS");
                var resultado = await processadorParcelas.ProcessarArquivoAsync(caminhoTxt);
                ExibirResultadoProcessamentoModular(resultado, "PARCELAS");
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"Tipo de arquivo não reconhecido: {nome}");
                Console.ResetColor();
            }
        }

        private static void ExibirResultadoProcessamentoModular<T>(FileProcessingResult<T> resultado, string tipoArquivo) where T : BaseEntity
        {
            if (resultado.Sucesso)
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"✅ {tipoArquivo} processado com sucesso!");
                Console.WriteLine($"📊 Total de registros: {resultado.Registros.Count}");
                Console.WriteLine($"✔️ Registros válidos: {resultado.Registros.Count(r => r.IsValida)}");
                Console.WriteLine($"❌ Registros inválidos: {resultado.Registros.Count(r => !r.IsValida)}");

                if (resultado.Estatisticas != null)
                {
                    Console.WriteLine($"📈 Estatísticas disponíveis");
                }
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Erro ao processar {tipoArquivo}:");
                Console.WriteLine($"🔍 {resultado.MensagemErro}");
            }
            Console.ResetColor();
        }
    }
} 