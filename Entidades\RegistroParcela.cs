using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa um registro do arquivo PARCELAS_OUT.txt / PARCELAS_FULL_OUT.txt
    /// Layout conforme especificação: 228 caracteres por linha
    /// </summary>
    public class RegistroParcela
    {
        /// <summary>
        /// Tipo de registro - posições 1-3 (sempre "000")
        /// </summary>
        [Required]
        [StringLength(3)]
        public string TipoRegistro { get; set; } = "000";

        /// <summary>
        /// Grupo - posição 4 (1=QuintoAndar, 2=QuintoCred)
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Grupo { get; set; } = string.Empty;

        /// <summary>
        /// Chave Cyber - posições 5-29 (25 caracteres)
        /// </summary>
        [Required]
        [StringLength(25)]
        public string ChaveCyber { get; set; } = string.Empty;

        /// <summary>
        /// ID da parcela - posições 30-44 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string IdParcela { get; set; } = string.Empty;

        /// <summary>
        /// Status da parcela - posição 45 
        /// 1=Ativa, 2=Paga, 3=Cancelada, 4=Pausa, 5=PrevNegociada, 6=EmNegociação
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Valor principal - posições 46-60 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorPrincipal { get; set; } = string.Empty;

        /// <summary>
        /// Valor dos juros - posições 61-75 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorJuros { get; set; } = string.Empty;

        /// <summary>
        /// Valor da multa - posições 76-90 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorMulta { get; set; } = string.Empty;

        /// <summary>
        /// Data de vencimento - posições 91-98 (8 caracteres - formato MMDDYYYY)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataVencimento { get; set; } = string.Empty;

        /// <summary>
        /// Valor das custas - posições 99-113 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorCustas { get; set; } = string.Empty;

        /// <summary>
        /// Valor dos honorários - posições 114-128 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorHonorarios { get; set; } = string.Empty;

        /// <summary>
        /// Status da fatura - posições 129-178 (50 caracteres)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string StatusFatura { get; set; } = string.Empty;

        /// <summary>
        /// Tipo da dívida - posições 179-228 (50 caracteres)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TipoDivida { get; set; } = string.Empty;

        /// <summary>
        /// Converte valor principal para decimal (divide por 100 para obter centavos)
        /// </summary>
        /// <returns>Valor principal como decimal</returns>
        public decimal ObterValorPrincipal()
        {
            if (string.IsNullOrEmpty(ValorPrincipal))
                return 0;

            var valor = long.Parse(ValorPrincipal.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte valor dos juros para decimal (divide por 100 para obter centavos)
        /// </summary>
        /// <returns>Valor dos juros como decimal</returns>
        public decimal ObterValorJuros()
        {
            if (string.IsNullOrEmpty(ValorJuros))
                return 0;

            var valor = long.Parse(ValorJuros.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte valor da multa para decimal (divide por 100 para obter centavos)
        /// </summary>
        /// <returns>Valor da multa como decimal</returns>
        public decimal ObterValorMulta()
        {
            if (string.IsNullOrEmpty(ValorMulta))
                return 0;

            var valor = long.Parse(ValorMulta.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte valor das custas para decimal (divide por 100 para obter centavos)
        /// </summary>
        /// <returns>Valor das custas como decimal</returns>
        public decimal ObterValorCustas()
        {
            if (string.IsNullOrEmpty(ValorCustas))
                return 0;

            var valor = long.Parse(ValorCustas.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte valor dos honorários para decimal (divide por 100 para obter centavos)
        /// </summary>
        /// <returns>Valor dos honorários como decimal</returns>
        public decimal ObterValorHonorarios()
        {
            if (string.IsNullOrEmpty(ValorHonorarios))
                return 0;

            var valor = long.Parse(ValorHonorarios.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Calcula o valor total da parcela (principal + juros + multa + custas + honorários)
        /// </summary>
        /// <returns>Valor total da parcela</returns>
        public decimal ObterValorTotal()
        {
            return ObterValorPrincipal() + ObterValorJuros() + ObterValorMulta() + 
                   ObterValorCustas() + ObterValorHonorarios();
        }

        /// <summary>
        /// Converte data de vencimento (MMDDYYYY) para DateTime
        /// </summary>
        /// <returns>Data de vencimento como DateTime</returns>
        public DateTime ObterDataVencimento()
        {
            if (string.IsNullOrEmpty(DataVencimento) || DataVencimento.Length != 8)
            {
                throw new ArgumentException("Data de vencimento deve ter 8 dígitos no formato MMDDYYYY");
            }

            var mes = int.Parse(DataVencimento.Substring(0, 2));
            var dia = int.Parse(DataVencimento.Substring(2, 2));
            var ano = int.Parse(DataVencimento.Substring(4, 4));

            return new DateTime(ano, mes, dia);
        }

        /// <summary>
        /// Obtém a descrição do status da parcela
        /// </summary>
        /// <returns>Descrição do status</returns>
        public string ObterDescricaoStatus()
        {
            return Status switch
            {
                "1" => "Ativa",
                "2" => "Paga",
                "3" => "Cancelada",
                "4" => "Pausa",
                "5" => "Prevista para Negociação",
                "6" => "Em Negociação",
                _ => "Status Desconhecido"
            };
        }

        /// <summary>
        /// Verifica se a parcela está vencida
        /// </summary>
        /// <returns>True se vencida, False caso contrário</returns>
        public bool EstaVencida()
        {
            try
            {
                var dataVencimento = ObterDataVencimento();
                return dataVencimento < DateTime.Now.Date;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras mínimas
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return TipoRegistro == "000" &&
                   (Grupo == "1" || Grupo == "2") &&
                   !string.IsNullOrEmpty(ChaveCyber) &&
                   !string.IsNullOrEmpty(IdParcela) &&
                   "123456".Contains(Status);
        }
    }
} 