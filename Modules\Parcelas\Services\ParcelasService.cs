using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SistemaMFT.Core.Entities;
using SistemaMFT.Modules.Parcelas.Entities;
using SistemaMFT.Modules.Parcelas.Processors;

namespace SistemaMFT.Modules.Parcelas.Services
{
    /// <summary>
    /// Serviço de alto nível para coordenar o processamento de parcelas
    /// Fornece funcionalidades de processamento, estatísticas e geração de relatórios
    /// Mantém independência do módulo e facilita integração com outros sistemas
    /// </summary>
    public class ParcelasService
    {
        private readonly ParcelasProcessor _processor;

        /// <summary>
        /// Construtor do serviço de parcelas
        /// </summary>
        public ParcelasService()
        {
            _processor = new ParcelasProcessor();
        }

        /// <summary>
        /// Processa um arquivo de parcelas e retorna estatísticas
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo de parcelas</param>
        /// <returns>Resultado do processamento com estatísticas</returns>
        public async Task<EstatisticasParcelas> ProcessarArquivoAsync(string caminhoArquivo)
        {
            try
            {
                Console.WriteLine($"[PARCELAS-SERVICE] Iniciando processamento: {Path.GetFileName(caminhoArquivo)}");

                var resultado = await _processor.ProcessarArquivoAsync(caminhoArquivo);

                if (!resultado.ProcessadoComSucesso)
                {
                    throw new Exception($"Falha no processamento do arquivo: {caminhoArquivo}");
                }

                var estatisticas = GerarEstatisticas(resultado);

                Console.WriteLine($"[PARCELAS-SERVICE] Processamento concluído com sucesso");
                LogarEstatisticas(estatisticas);

                return estatisticas;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERRO] Erro no serviço de parcelas: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gera estatísticas detalhadas do arquivo processado
        /// </summary>
        /// <param name="resultado">Resultado do processamento</param>
        /// <returns>Estatísticas das parcelas</returns>
        public EstatisticasParcelas GerarEstatisticas(FileProcessingResult<RegistroParcela> resultado)
        {
            var parcelas = resultado.RegistrosValidos;

            var estatisticas = new EstatisticasParcelas
            {
                NomeArquivo = resultado.NomeArquivo,
                DataProcessamento = resultado.DataProcessamento,
                TotalLinhasProcessadas = resultado.TotalLinhas,
                TotalRegistrosValidos = resultado.TotalValidos,
                TotalRegistrosInvalidos = resultado.TotalErros,
                Erros = resultado.RegistrosComErro.SelectMany(r => r.Erros).ToList()
            };

            if (parcelas.Any())
            {
                // Estatísticas por status
                estatisticas.TotalParcelas = parcelas.Count;
                estatisticas.ParcelasAbertas = parcelas.Count(p => p.IsAberta);
                estatisticas.ParcelasPagas = parcelas.Count(p => p.IsPaga);
                estatisticas.ParcelasCanceladas = parcelas.Count(p => p.IsCancelada);

                // Estatísticas por grupo
                estatisticas.ParcelasQuintoAndar = parcelas.Count(p => p.Grupo == "1");
                estatisticas.ParcelasQuintoCred = parcelas.Count(p => p.Grupo == "2");

                // Estatísticas de atraso
                estatisticas.ParcelasEmAtraso = parcelas.Count(p => p.DiasAtraso > 0);
                estatisticas.ParcelasEmDia = parcelas.Count(p => p.DiasAtraso == 0 && p.IsAberta);

                // Valores financeiros
                estatisticas.ValorTotalPrincipal = parcelas.Sum(p => p.ValorPrincipalDecimal);
                estatisticas.ValorTotalJuros = parcelas.Sum(p => p.ValorJurosDecimal);
                estatisticas.ValorTotalMulta = parcelas.Sum(p => p.ValorMultaDecimal);
                estatisticas.ValorTotalGeral = parcelas.Sum(p => p.ValorTotal);
                estatisticas.ValorTotalPago = parcelas.Sum(p => p.ValorPagoDecimal);

                // Valores por status
                estatisticas.ValorParcelasAbertas = parcelas.Where(p => p.IsAberta).Sum(p => p.ValorTotal);
                estatisticas.ValorParcelasPagas = parcelas.Where(p => p.IsPaga).Sum(p => p.ValorTotal);

                // Estatísticas de contratos
                estatisticas.TotalContratos = parcelas.Select(p => p.NumeroContrato).Distinct().Count();

                // Média de parcelas por contrato
                if (estatisticas.TotalContratos > 0)
                {
                    estatisticas.MediaParcelasPorContrato = (double)estatisticas.TotalParcelas / estatisticas.TotalContratos;
                }

                // Inconsistências
                estatisticas.ParcelasPagasSemData = parcelas.Count(p => p.IsPaga && !p.TemDataPagamento);
                estatisticas.ParcelasComDataSemValor = parcelas.Count(p => p.TemDataPagamento && p.ValorPagoDecimal == 0);
            }

            return estatisticas;
        }

        /// <summary>
        /// Exporta parcelas para CSV
        /// </summary>
        /// <param name="parcelas">Lista de parcelas</param>
        /// <param name="caminhoArquivo">Caminho para salvar o CSV</param>
        /// <returns>True se exportado com sucesso</returns>
        public async Task<bool> ExportarParaCSVAsync(List<RegistroParcela> parcelas, string caminhoArquivo)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Cabeçalho
                csv.AppendLine("NumeroContrato,Grupo,GrupoDescricao,NumeroParcela,DataVencimento,DataVencimentoFormatada,ValorPrincipal,ValorJuros,ValorMulta,ValorTotal,StatusParcela,StatusDescricao,DataPagamento,DataPagamentoFormatada,ValorPago,Ranking,RankingNumerico,SituacaoAtraso,DiasAtraso");

                // Dados
                foreach (var parcela in parcelas)
                {
                    csv.AppendLine($"\"{parcela.NumeroContrato}\",\"{parcela.Grupo}\",\"{parcela.GrupoDescricao}\",{parcela.NumeroParcelaInt},\"{parcela.DataVencimento}\",\"{parcela.DataVencimentoFormatada}\",{parcela.ValorPrincipalDecimal:F2},{parcela.ValorJurosDecimal:F2},{parcela.ValorMultaDecimal:F2},{parcela.ValorTotal:F2},\"{parcela.StatusParcela}\",\"{parcela.StatusDescricao}\",\"{parcela.DataPagamento}\",\"{parcela.DataPagamentoFormatada}\",{parcela.ValorPagoDecimal:F2},\"{parcela.Ranking}\",{parcela.RankingNumerico},\"{parcela.SituacaoAtraso}\",{parcela.DiasAtraso}");
                }

                await File.WriteAllTextAsync(caminhoArquivo, csv.ToString(), Encoding.UTF8);
                Console.WriteLine($"[PARCELAS-SERVICE] CSV exportado: {caminhoArquivo}");
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERRO] Falha ao exportar CSV: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Obtém parcelas em atraso
        /// </summary>
        /// <param name="parcelas">Lista de parcelas</param>
        /// <returns>Parcelas em atraso ordenadas por dias de atraso</returns>
        public List<RegistroParcela> ObterParcelasEmAtraso(List<RegistroParcela> parcelas)
        {
            return parcelas
                .Where(p => p.DiasAtraso > 0)
                .OrderByDescending(p => p.DiasAtraso)
                .ThenBy(p => p.NumeroContrato)
                .ToList();
        }

        /// <summary>
        /// Obtém parcelas por contrato
        /// </summary>
        /// <param name="parcelas">Lista de parcelas</param>
        /// <param name="numeroContrato">Número do contrato</param>
        /// <returns>Parcelas do contrato ordenadas por número da parcela</returns>
        public List<RegistroParcela> ObterParcelasPorContrato(List<RegistroParcela> parcelas, string numeroContrato)
        {
            return parcelas
                .Where(p => p.NumeroContrato.Equals(numeroContrato, StringComparison.OrdinalIgnoreCase))
                .OrderBy(p => p.NumeroParcelaInt)
                .ToList();
        }

        /// <summary>
        /// Obtém resumo por contrato
        /// </summary>
        /// <param name="parcelas">Lista de parcelas</param>
        /// <returns>Resumo agrupado por contrato</returns>
        public List<ResumoContrato> ObterResumoPorContrato(List<RegistroParcela> parcelas)
        {
            return parcelas
                .GroupBy(p => p.NumeroContrato)
                .Select(g => new ResumoContrato
                {
                    NumeroContrato = g.Key,
                    TotalParcelas = g.Count(),
                    ParcelasAbertas = g.Count(p => p.IsAberta),
                    ParcelasPagas = g.Count(p => p.IsPaga),
                    ParcelasCanceladas = g.Count(p => p.IsCancelada),
                    ParcelasEmAtraso = g.Count(p => p.DiasAtraso > 0),
                    ValorTotal = g.Sum(p => p.ValorTotal),
                    ValorPago = g.Sum(p => p.ValorPagoDecimal),
                    ValorEmAberto = g.Where(p => p.IsAberta).Sum(p => p.ValorTotal),
                    MaiorAtraso = g.Max(p => p.DiasAtraso),
                    Grupo = g.First().Grupo,
                    GrupoDescricao = g.First().GrupoDescricao
                })
                .OrderBy(r => r.NumeroContrato)
                .ToList();
        }

        #region Métodos Privados

        /// <summary>
        /// Loga as estatísticas geradas
        /// </summary>
        private void LogarEstatisticas(EstatisticasParcelas stats)
        {
            Console.WriteLine("\n=== ESTATÍSTICAS DE PARCELAS ===");
            Console.WriteLine($"Arquivo: {stats.NomeArquivo}");
            Console.WriteLine($"Processado em: {stats.DataProcessamento:dd/MM/yyyy HH:mm:ss}");
            Console.WriteLine($"Total de linhas: {stats.TotalLinhasProcessadas}");
            Console.WriteLine($"Registros válidos: {stats.TotalRegistrosValidos}");
            Console.WriteLine($"Registros inválidos: {stats.TotalRegistrosInvalidos}");
            
            if (stats.TotalParcelas > 0)
            {
                Console.WriteLine($"\n--- RESUMO GERAL ---");
                Console.WriteLine($"Total de parcelas: {stats.TotalParcelas:N0}");
                Console.WriteLine($"Total de contratos: {stats.TotalContratos:N0}");
                Console.WriteLine($"Média parcelas/contrato: {stats.MediaParcelasPorContrato:F1}");
                
                Console.WriteLine($"\n--- POR STATUS ---");
                Console.WriteLine($"Abertas: {stats.ParcelasAbertas:N0} ({(double)stats.ParcelasAbertas/stats.TotalParcelas*100:F1}%)");
                Console.WriteLine($"Pagas: {stats.ParcelasPagas:N0} ({(double)stats.ParcelasPagas/stats.TotalParcelas*100:F1}%)");
                Console.WriteLine($"Canceladas: {stats.ParcelasCanceladas:N0} ({(double)stats.ParcelasCanceladas/stats.TotalParcelas*100:F1}%)");
                
                Console.WriteLine($"\n--- POR GRUPO ---");
                Console.WriteLine($"QuintoAndar: {stats.ParcelasQuintoAndar:N0} ({(double)stats.ParcelasQuintoAndar/stats.TotalParcelas*100:F1}%)");
                Console.WriteLine($"QuintoCred: {stats.ParcelasQuintoCred:N0} ({(double)stats.ParcelasQuintoCred/stats.TotalParcelas*100:F1}%)");
                
                Console.WriteLine($"\n--- SITUAÇÃO DE PAGAMENTO ---");
                Console.WriteLine($"Em dia: {stats.ParcelasEmDia:N0}");
                Console.WriteLine($"Em atraso: {stats.ParcelasEmAtraso:N0}");
                
                Console.WriteLine($"\n--- VALORES FINANCEIROS ---");
                Console.WriteLine($"Valor total geral: {stats.ValorTotalGeral:C2}");
                Console.WriteLine($"Valor principal: {stats.ValorTotalPrincipal:C2}");
                Console.WriteLine($"Valor juros: {stats.ValorTotalJuros:C2}");
                Console.WriteLine($"Valor multa: {stats.ValorTotalMulta:C2}");
                Console.WriteLine($"Valor pago: {stats.ValorTotalPago:C2}");
                Console.WriteLine($"Valor em aberto: {stats.ValorParcelasAbertas:C2}");
                
                if (stats.ParcelasPagasSemData > 0 || stats.ParcelasComDataSemValor > 0)
                {
                    Console.WriteLine($"\n--- INCONSISTÊNCIAS ---");
                    if (stats.ParcelasPagasSemData > 0)
                        Console.WriteLine($"Pagas sem data: {stats.ParcelasPagasSemData:N0}");
                    if (stats.ParcelasComDataSemValor > 0)
                        Console.WriteLine($"Com data sem valor: {stats.ParcelasComDataSemValor:N0}");
                }
            }
            
            if (stats.Erros.Any())
            {
                Console.WriteLine($"\n--- ERROS ({stats.Erros.Count}) ---");
                foreach (var erro in stats.Erros.Take(10))
                {
                    Console.WriteLine($"• {erro}");
                }
                if (stats.Erros.Count > 10)
                {
                    Console.WriteLine($"... e mais {stats.Erros.Count - 10} erros");
                }
            }
            
            Console.WriteLine("================================\n");
        }

        #endregion
    }

    /// <summary>
    /// Classe para estatísticas específicas de parcelas
    /// </summary>
    public class EstatisticasParcelas
    {
        public string NomeArquivo { get; set; } = string.Empty;
        public DateTime DataProcessamento { get; set; }
        public int TotalLinhasProcessadas { get; set; }
        public int TotalRegistrosValidos { get; set; }
        public int TotalRegistrosInvalidos { get; set; }
        public List<string> Erros { get; set; } = new();

        // Estatísticas específicas de parcelas
        public int TotalParcelas { get; set; }
        public int TotalContratos { get; set; }
        public double MediaParcelasPorContrato { get; set; }

        // Por status
        public int ParcelasAbertas { get; set; }
        public int ParcelasPagas { get; set; }
        public int ParcelasCanceladas { get; set; }

        // Por grupo
        public int ParcelasQuintoAndar { get; set; }
        public int ParcelasQuintoCred { get; set; }

        // Situação de pagamento
        public int ParcelasEmDia { get; set; }
        public int ParcelasEmAtraso { get; set; }

        // Valores financeiros
        public decimal ValorTotalPrincipal { get; set; }
        public decimal ValorTotalJuros { get; set; }
        public decimal ValorTotalMulta { get; set; }
        public decimal ValorTotalGeral { get; set; }
        public decimal ValorTotalPago { get; set; }
        public decimal ValorParcelasAbertas { get; set; }
        public decimal ValorParcelasPagas { get; set; }

        // Inconsistências
        public int ParcelasPagasSemData { get; set; }
        public int ParcelasComDataSemValor { get; set; }
    }

    /// <summary>
    /// Classe para resumo por contrato
    /// </summary>
    public class ResumoContrato
    {
        public string NumeroContrato { get; set; } = string.Empty;
        public string Grupo { get; set; } = string.Empty;
        public string GrupoDescricao { get; set; } = string.Empty;
        public int TotalParcelas { get; set; }
        public int ParcelasAbertas { get; set; }
        public int ParcelasPagas { get; set; }
        public int ParcelasCanceladas { get; set; }
        public int ParcelasEmAtraso { get; set; }
        public decimal ValorTotal { get; set; }
        public decimal ValorPago { get; set; }
        public decimal ValorEmAberto { get; set; }
        public int MaiorAtraso { get; set; }
    }
}
