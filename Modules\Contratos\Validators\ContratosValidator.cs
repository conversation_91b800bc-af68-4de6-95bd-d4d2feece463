using System;
using System.Collections.Generic;
using System.Linq;
using SistemaMFT.Core.Interfaces;
using SistemaMFT.Core.Common;
using SistemaMFT.Modules.Contratos.Entities;

namespace SistemaMFT.Modules.Contratos.Validators
{
    /// <summary>
    /// Validador específico para registros de contratos
    /// Implementa todas as regras de validação conforme documentação
    /// Independente de outros módulos, contém apenas lógica específica de contratos
    /// </summary>
    public class ContratosValidator : IValidator<RegistroContrato>
    {
        /// <summary>
        /// Valida um registro de contrato completo
        /// Aplica todas as regras de negócio específicas para contratos
        /// </summary>
        /// <param name="contrato">Registro de contrato a ser validado</param>
        /// <returns>Resultado da validação com erros e avisos</returns>
        public ValidationResult Validar(RegistroContrato contrato)
        {
            var resultado = new ValidationResult();

            if (contrato == null)
            {
                resultado.Erros.Add("Registro de contrato não pode ser nulo");
                return resultado;
            }

            // Validação do tipo de registro
            ValidarTipoRegistro(contrato.TipoRegistro, resultado);

            // Validação do grupo
            ValidarGrupo(contrato.Grupo, resultado);

            // Validação do número do contrato
            ValidarNumeroContrato(contrato.NumeroContrato, resultado);

            // Validação do CPF/CNPJ
            ValidarCpfCnpj(contrato.CpfCnpj, resultado);

            // Validação do nome do cliente
            ValidarNomeCliente(contrato.NomeCliente, resultado);

            // Validação do status do contrato
            ValidarStatusContrato(contrato.StatusContrato, resultado);

            // Validação das datas
            ValidarDatas(contrato.DataInicio, contrato.DataFim, resultado);

            // Validação do valor do contrato
            ValidarValorContrato(contrato.ValorContrato, resultado);

            // Validações de consistência entre campos
            ValidarConsistenciaGeral(contrato, resultado);

            resultado.IsValid = resultado.Erros.Count == 0;
            return resultado;
        }

        /// <summary>
        /// Valida um campo específico do registro de contrato
        /// </summary>
        /// <param name="valor">Valor do campo</param>
        /// <param name="nomeCampo">Nome do campo</param>
        /// <returns>Resultado da validação do campo</returns>
        public FieldValidationResult ValidarCampo(string valor, string nomeCampo)
        {
            var resultado = new FieldValidationResult();

            switch (nomeCampo.ToLowerInvariant())
            {
                case "tiporegistro":
                    resultado.IsValid = valor == "100";
                    resultado.Erro = resultado.IsValid ? string.Empty : "Tipo de registro deve ser '100'";
                    resultado.ValorFormatado = valor;
                    break;

                case "grupo":
                    resultado.IsValid = ValidationHelper.ValidarValoresPermitidos(valor, "1", "2");
                    resultado.Erro = resultado.IsValid ? string.Empty : "Grupo deve ser '1' (Locação) ou '2' (Crédito)";
                    resultado.ValorFormatado = valor == "1" ? "QuintoAndar - Locação" : 
                                             valor == "2" ? "QuintoCred - Crédito" : valor;
                    break;

                case "cpfcnpj":
                    if (valor?.Length == 11)
                    {
                        var validacaoCpf = ValidationHelper.ValidarCPF(valor);
                        resultado.IsValid = validacaoCpf.valido;
                        resultado.Erro = validacaoCpf.erro;
                        resultado.ValorFormatado = StringHelper.FormatarCPF(valor);
                    }
                    else if (valor?.Length == 14)
                    {
                        var validacaoCnpj = ValidationHelper.ValidarCNPJ(valor);
                        resultado.IsValid = validacaoCnpj.valido;
                        resultado.Erro = validacaoCnpj.erro;
                        resultado.ValorFormatado = StringHelper.FormatarCNPJ(valor);
                    }
                    else
                    {
                        resultado.IsValid = false;
                        resultado.Erro = "CPF deve ter 11 dígitos ou CNPJ deve ter 14 dígitos";
                        resultado.ValorFormatado = valor ?? string.Empty;
                    }
                    break;

                case "statuscontrato":
                    resultado.IsValid = ValidationHelper.ValidarValoresPermitidos(valor, "A", "I", "C");
                    resultado.Erro = resultado.IsValid ? string.Empty : "Status deve ser 'A' (Ativo), 'I' (Inativo) ou 'C' (Cancelado)";
                    resultado.ValorFormatado = valor switch
                    {
                        "A" => "Ativo",
                        "I" => "Inativo",
                        "C" => "Cancelado",
                        _ => valor ?? string.Empty
                    };
                    break;

                case "datainicio":
                case "datafim":
                    var validacaoData = ValidationHelper.ValidarDataMMDDAAAA(valor);
                    resultado.IsValid = validacaoData.valido;
                    resultado.Erro = validacaoData.erro;
                    resultado.ValorFormatado = validacaoData.valido ? 
                        validacaoData.dataFormatada?.ToString("dd/MM/yyyy") ?? string.Empty : 
                        valor ?? string.Empty;
                    break;

                case "valorcontrato":
                    var validacaoValor = ValidationHelper.ValidarValorMonetario(valor, 2);
                    resultado.IsValid = validacaoValor.valido;
                    resultado.Erro = validacaoValor.erro;
                    resultado.ValorFormatado = validacaoValor.valido ? 
                        StringHelper.FormatarMoeda(validacaoValor.valorFormatado ?? 0) : 
                        valor ?? string.Empty;
                    break;

                default:
                    resultado.IsValid = ValidationHelper.NaoVazio(valor);
                    resultado.Erro = resultado.IsValid ? string.Empty : "Campo não pode estar vazio";
                    resultado.ValorFormatado = StringHelper.LimparEspacos(valor);
                    break;
            }

            return resultado;
        }

        /// <summary>
        /// Obtém todas as regras de validação aplicáveis a contratos
        /// </summary>
        /// <returns>Lista de regras de validação</returns>
        public IEnumerable<ValidationRule> ObterRegrasValidacao()
        {
            return new List<ValidationRule>
            {
                new ValidationRule { Campo = "TipoRegistro", Regra = "Valor fixo", Descricao = "Deve ser sempre '100'" },
                new ValidationRule { Campo = "Grupo", Regra = "Valores permitidos", Descricao = "'1' para Locação ou '2' para Crédito" },
                new ValidationRule { Campo = "NumeroContrato", Regra = "Obrigatório", Descricao = "Não pode estar vazio" },
                new ValidationRule { Campo = "CpfCnpj", Regra = "CPF/CNPJ válido", Descricao = "11 dígitos para CPF ou 14 para CNPJ com dígitos verificadores corretos" },
                new ValidationRule { Campo = "NomeCliente", Regra = "Tamanho mínimo", Descricao = "Mínimo 2 caracteres" },
                new ValidationRule { Campo = "StatusContrato", Regra = "Valores permitidos", Descricao = "'A' (Ativo), 'I' (Inativo) ou 'C' (Cancelado)" },
                new ValidationRule { Campo = "DataInicio", Regra = "Data válida", Descricao = "Formato MMDDAAAA com data válida" },
                new ValidationRule { Campo = "DataFim", Regra = "Data válida", Descricao = "Formato MMDDAAAA com data válida e posterior à data de início" },
                new ValidationRule { Campo = "ValorContrato", Regra = "Valor monetário", Descricao = "Valor numérico positivo em centavos" }
            };
        }

        #region Métodos de Validação Específicos

        private void ValidarTipoRegistro(string tipoRegistro, ValidationResult resultado)
        {
            if (tipoRegistro != "100")
            {
                resultado.Erros.Add($"Tipo de registro inválido: '{tipoRegistro}'. Esperado: '100'");
            }
        }

        private void ValidarGrupo(string grupo, ValidationResult resultado)
        {
            if (!ValidationHelper.ValidarValoresPermitidos(grupo, "1", "2"))
            {
                resultado.Erros.Add($"Grupo inválido: '{grupo}'. Valores permitidos: '1' (Locação), '2' (Crédito)");
            }
        }

        private void ValidarNumeroContrato(string numeroContrato, ValidationResult resultado)
        {
            if (!ValidationHelper.NaoVazio(numeroContrato))
            {
                resultado.Erros.Add("Número do contrato não pode estar vazio");
            }
            else if (numeroContrato.Trim().Length < 3)
            {
                resultado.Avisos.Add($"Número do contrato muito curto: '{numeroContrato}'");
            }
        }

        private void ValidarCpfCnpj(string cpfCnpj, ValidationResult resultado)
        {
            if (!ValidationHelper.NaoVazio(cpfCnpj))
            {
                resultado.Erros.Add("CPF/CNPJ não pode estar vazio");
                return;
            }

            var documento = cpfCnpj.Trim();
            
            if (documento.Length == 11)
            {
                var validacao = ValidationHelper.ValidarCPF(documento);
                if (!validacao.valido)
                {
                    resultado.Erros.Add($"CPF inválido: {validacao.erro}");
                }
            }
            else if (documento.Length == 14)
            {
                var validacao = ValidationHelper.ValidarCNPJ(documento);
                if (!validacao.valido)
                {
                    resultado.Erros.Add($"CNPJ inválido: {validacao.erro}");
                }
            }
            else
            {
                resultado.Erros.Add($"Documento inválido: {documento.Length} dígitos. Esperado: 11 (CPF) ou 14 (CNPJ)");
            }
        }

        private void ValidarNomeCliente(string nomeCliente, ValidationResult resultado)
        {
            if (!ValidationHelper.NaoVazio(nomeCliente))
            {
                resultado.Erros.Add("Nome do cliente não pode estar vazio");
            }
            else if (nomeCliente.Trim().Length < 2)
            {
                resultado.Erros.Add($"Nome muito curto: '{nomeCliente}'. Mínimo 2 caracteres");
            }
        }

        private void ValidarStatusContrato(string statusContrato, ValidationResult resultado)
        {
            if (!ValidationHelper.ValidarValoresPermitidos(statusContrato, "A", "I", "C"))
            {
                resultado.Erros.Add($"Status inválido: '{statusContrato}'. Valores permitidos: 'A' (Ativo), 'I' (Inativo), 'C' (Cancelado)");
            }
        }

        private void ValidarDatas(string dataInicio, string dataFim, ValidationResult resultado)
        {
            var validacaoInicio = ValidationHelper.ValidarDataMMDDAAAA(dataInicio);
            var validacaoFim = ValidationHelper.ValidarDataMMDDAAAA(dataFim);

            if (!validacaoInicio.valido)
            {
                resultado.Erros.Add($"Data de início inválida: {validacaoInicio.erro}");
            }

            if (!validacaoFim.valido)
            {
                resultado.Erros.Add($"Data de fim inválida: {validacaoFim.erro}");
            }

            // Validação de consistência entre datas
            if (validacaoInicio.valido && validacaoFim.valido && 
                validacaoInicio.dataFormatada.HasValue && validacaoFim.dataFormatada.HasValue)
            {
                if (validacaoFim.dataFormatada <= validacaoInicio.dataFormatada)
                {
                    resultado.Erros.Add($"Data de fim ({validacaoFim.dataFormatada:dd/MM/yyyy}) deve ser posterior à data de início ({validacaoInicio.dataFormatada:dd/MM/yyyy})");
                }
            }
        }

        private void ValidarValorContrato(string valorContrato, ValidationResult resultado)
        {
            var validacao = ValidationHelper.ValidarValorMonetario(valorContrato, 2);
            
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Valor do contrato inválido: {validacao.erro}");
            }
            else if (validacao.valorFormatado <= 0)
            {
                resultado.Avisos.Add("Valor do contrato é zero ou negativo");
            }
        }

        private void ValidarConsistenciaGeral(RegistroContrato contrato, ValidationResult resultado)
        {
            // Validação específica: contratos cancelados devem ter observações
            if (contrato.StatusContrato == "C" && string.IsNullOrWhiteSpace(contrato.NomeCliente))
            {
                resultado.Avisos.Add("Contrato cancelado sem nome do cliente pode indicar problema nos dados");
            }

            // Validação de valor muito alto (possível erro de digitação)
            if (contrato.ObterValorContrato() > 1000000) // Mais de 1 milhão
            {
                resultado.Avisos.Add($"Valor do contrato muito alto: {StringHelper.FormatarMoeda(contrato.ObterValorContrato())}");
            }
        }

        #endregion
    }
}
