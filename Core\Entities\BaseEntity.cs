using System;
using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Core.Entities
{
    /// <summary>
    /// Classe base para todas as entidades do sistema
    /// Fornece propriedades comuns que todas as entidades devem ter
    /// Garante consistência entre módulos sem criar dependências
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// Identificador único da entidade
        /// Gerado automaticamente para controle interno
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Data e hora de criação da entidade
        /// Registra quando a entidade foi processada
        /// </summary>
        public DateTime DataCriacao { get; set; } = DateTime.Now;

        /// <summary>
        /// Número da linha no arquivo original
        /// Usado para rastreamento e debugging
        /// </summary>
        public int NumeroLinha { get; set; }

        /// <summary>
        /// Indica se a entidade passou por todas as validações
        /// Usado para filtrar registros válidos/inválidos
        /// </summary>
        public bool IsValida { get; set; } = true;

        /// <summary>
        /// Linha original do arquivo antes do processamento
        /// Mantida para auditoria e debugging
        /// </summary>
        [Required]
        public string LinhaOriginal { get; set; } = string.Empty;

        /// <summary>
        /// Obtém uma representação string básica da entidade
        /// Cada módulo pode sobrescrever para personalizar
        /// </summary>
        /// <returns>String representando a entidade</returns>
        public virtual string ObterResumo()
        {
            return $"[{GetType().Name}] ID: {Id} | Linha: {NumeroLinha} | Válida: {IsValida}";
        }

        /// <summary>
        /// Valida se a entidade atende aos requisitos mínimos
        /// Validação básica que todas as entidades devem passar
        /// </summary>
        /// <returns>True se a entidade é válida</returns>
        public virtual bool ValidarBase()
        {
            return !string.IsNullOrWhiteSpace(LinhaOriginal) && NumeroLinha > 0;
        }
    }
}
