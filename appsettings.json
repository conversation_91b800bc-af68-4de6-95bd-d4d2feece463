{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConfiguracaoMFT": {"ServidorMFT": {"Host": "mft.toppen.com.br", "Porta": 8448, "UsarHTTPS": true, "TimeoutSegundos": 60}, "ServidorSFTP": {"Host": "mft.toppen.com.br", "Porta": 228, "Usuario": "gondim", "Senha": "M@caco13", "DiretorioRemoto": "/homologacao/saida", "TimeoutSegundos": 30}, "ConfiguracaoGPG": {"EmailDestinatario": "<EMAIL>", "CaminhoChavePublica": "Configuracao/chaves/chave_publica.asc", "CaminhoChavePrivada": "Configuracao/chaves/chave_privada.asc", "SenhaChavePrivada": ""}, "ConfiguracaoArquivos": {"CodificacaoArquivos": "ISO-8859-1", "DiretorioEntrada": "Arquivos/Entrada", "DiretorioSaida": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>a", "DiretorioProcessados": "Arquivos/Processados", "DiretorioErros": "Arquivos/Erros", "PrefixoBackup": "backup_", "FormatoDataArquivo": "ddMMyyyy"}, "ConfiguracaoFrequencias": {"ArquivosDiarios": ["PARCELAS_OUT", "BILLITENS_OUT", "CONTRATOS_OUT", "RELATIONS_OUT", "TELEFONE_OUT", "ENDERECO_OUT", "ACORDOS_OUT", "PAGAMENTO_OUT", "INFORMACAO_ACORDO_OUT", "RETIRADACONTRATOS_OUT", "BOLETAGEM_REJEITADOS_OUT", "ACIONAMENTO_ANTERIOR_OUT", "BOLETAGEM_OUT"], "ArquivosSemanais": ["PARCELAS_FULL_OUT", "BILLITENS_FULL_OUT", "CONTRATOS_FULL_OUT"], "ArquivosHorarios": ["PAGAMENTOD0_OUT"]}}, "ConfiguracaoAPI": {"CyberAgreements": {"Host": "cyber27h01.toppen.com.br", "Authorization": "Basic UUFfR1JCOkBRdWludG8xMjQ=", "ContentType": "application/json", "TimeoutMinutos": 15, "SequenciaObrigatoria": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "consultaElegibilidade", "consultaAmortizacao", "cadastraAcordo", "geraMeioPagamento", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}