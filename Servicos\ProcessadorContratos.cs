using System;
using System.Collections.Generic;
using SistemaMFT.Entidades;
using SistemaMFT.Utilitarios;

namespace SistemaMFT.Servicos
{
    /// <summary>
    /// Processador específico para arquivos de CONTRATOS conforme documentação
    /// Nome do Arquivo: YYYYMMDD_HHMMSS_REMESSA_CONTRATOS_OUT.txt
    /// Código de Registro: "100"
    /// Layout: 155 caracteres por linha
    /// </summary>
    public class ProcessadorContratos
    {
        /// <summary>
        /// Processa uma linha do arquivo de contratos seguindo o protocolo da documentação
        /// </summary>
        /// <param name="linha">Linha do arquivo</param>
        /// <param name="numeroLinha">Número da linha para log</param>
        /// <returns>RegistroContrato processado ou null se inválido</returns>
        public static RegistroContrato ProcessarLinhaContrato(string linha, int numeroLinha)
        {
            const string entidade = "CONTRATO";
            Console.WriteLine($"\n=== PROCESSANDO LINHA {numeroLinha} - ENTIDADE: {entidade} ===");
            
            // Verificar tamanho mínimo da linha
            if (linha.Length < 155)
            {
                Console.WriteLine($"[ERRO] Linha muito curta: {linha.Length} caracteres. Mínimo esperado: 155");
                return null;
            }
            
            var contrato = new RegistroContrato();
            
            // 1. TIPO DE REGISTRO (Posição 1-3)
            contrato.TipoRegistro = linha.Substring(0, 3);
            var validacaoTipo = contrato.TipoRegistro == "100" ? 
                (true, (string)null) : 
                (false, $"Tipo de registro inválido: '{contrato.TipoRegistro}'. Esperado: '100'");
            
            Console.WriteLine($"[ENTIDADE: {entidade}] tipoRegistro: '{contrato.TipoRegistro}' | Posição: 1-3 | Status: {(validacaoTipo.Item1 ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoTipo.Item2 ?? "Nenhum"}");
            
            if (!validacaoTipo.Item1) return null;
            
            // 2. GRUPO (Posição 4)
            contrato.Grupo = linha.Substring(3, 1);
            var validacaoGrupo = ValidadoresUniversais.ValidarStatus(contrato.Grupo, 
                new[] { "1", "2" }, 
                new Dictionary<string, string> 
                {
                    { "1", "QuintoAndar - Locação" },
                    { "2", "QuintoCred - Crédito" }
                });
            
            Console.WriteLine($"[ENTIDADE: {entidade}] grupo: '{contrato.Grupo}' | Posição: 4-4 | Status: {(validacaoGrupo.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoGrupo.erro ?? "Nenhum"}");
            
            if (!validacaoGrupo.valido) return null;
            
            contrato.GrupoDescricao = validacaoGrupo.descricao;
            
            // 3. NÚMERO DO CONTRATO (Posição 5-29)
            contrato.NumeroContrato = linha.Substring(4, 25).Trim();
            var validacaoContrato = contrato.NumeroContrato.Length > 0 ? 
                (true, (string)null) : 
                (false, "Número do contrato não pode estar vazio");
            
            Console.WriteLine($"[ENTIDADE: {entidade}] numeroContrato: '{contrato.NumeroContrato}' | Posição: 5-29 | Status: {(validacaoContrato.Item1 ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoContrato.Item2 ?? "Nenhum"}");
            
            if (!validacaoContrato.Item1) return null;
            
            // 4. CPF/CNPJ (Posição 30-43)
            contrato.CpfCnpj = linha.Substring(29, 14).Trim();
            
            if (contrato.CpfCnpj.Length == 11)
            {
                var validacaoCpf = ValidadoresUniversais.ValidarCPF(contrato.CpfCnpj);
                Console.WriteLine($"[ENTIDADE: {entidade}] cpfCnpj: '{contrato.CpfCnpj}' | Posição: 30-43 | Status: {(validacaoCpf.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoCpf.erro ?? "Nenhum"}");
                
                if (!validacaoCpf.valido) return null;
                
                contrato.TipoDocumento = "CPF";
                contrato.DocumentoFormatado = validacaoCpf.cpfFormatado;
            }
            else if (contrato.CpfCnpj.Length == 14)
            {
                var validacaoCnpj = ValidadoresUniversais.ValidarCNPJ(contrato.CpfCnpj);
                Console.WriteLine($"[ENTIDADE: {entidade}] cpfCnpj: '{contrato.CpfCnpj}' | Posição: 30-43 | Status: {(validacaoCnpj.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoCnpj.erro ?? "Nenhum"}");
                
                if (!validacaoCnpj.valido) return null;
                
                contrato.TipoDocumento = "CNPJ";
                contrato.DocumentoFormatado = validacaoCnpj.cnpjFormatado;
            }
            else
            {
                Console.WriteLine($"[ENTIDADE: {entidade}] cpfCnpj: '{contrato.CpfCnpj}' | Posição: 30-43 | Status: INVÁLIDO | Erro: Documento inválido: {contrato.CpfCnpj.Length} dígitos. Esperado: 11 (CPF) ou 14 (CNPJ)");
                return null;
            }
            
            // 5. NOME DO CLIENTE (Posição 44-123)
            contrato.NomeCliente = linha.Substring(43, 80).Trim();
            var validacaoNome = contrato.NomeCliente.Length >= 2 ? 
                (true, (string)null) : 
                (false, $"Nome muito curto: '{contrato.NomeCliente}'. Mínimo 2 caracteres");
            
            Console.WriteLine($"[ENTIDADE: {entidade}] nomeCliente: '{contrato.NomeCliente}' | Posição: 44-123 | Status: {(validacaoNome.Item1 ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoNome.Item2 ?? "Nenhum"}");
            
            if (!validacaoNome.Item1) return null;

            // 6. STATUS DO CONTRATO (Posição 124)
            contrato.StatusContrato = linha.Substring(123, 1);
            var validacaoStatus = ValidadoresUniversais.ValidarStatus(contrato.StatusContrato,
                new[] { "A", "I", "C" },
                new Dictionary<string, string>
                {
                    { "A", "Ativo" },
                    { "I", "Inativo" },
                    { "C", "Cancelado" }
                });

            Console.WriteLine($"[ENTIDADE: {entidade}] statusContrato: '{contrato.StatusContrato}' | Posição: 124-124 | Status: {(validacaoStatus.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoStatus.erro ?? "Nenhum"}");

            if (!validacaoStatus.valido) return null;

            contrato.StatusDescricao = validacaoStatus.descricao;

            // 7. DATA DE INÍCIO (Posição 125-132)
            contrato.DataInicio = linha.Substring(124, 8);
            var validacaoDataInicio = ValidadoresUniversais.ValidarDataMMDDAAAA(contrato.DataInicio);

            Console.WriteLine($"[ENTIDADE: {entidade}] dataInicio: '{contrato.DataInicio}' | Posição: 125-132 | Status: {(validacaoDataInicio.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoDataInicio.erro ?? "Nenhum"}");

            if (!validacaoDataInicio.valido) return null;

            contrato.DataInicioFormatada = validacaoDataInicio.dataFormatada;

            // 8. DATA DE FIM (Posição 133-140)
            contrato.DataFim = linha.Substring(132, 8);
            var validacaoDataFim = ValidadoresUniversais.ValidarDataMMDDAAAA(contrato.DataFim);

            Console.WriteLine($"[ENTIDADE: {entidade}] dataFim: '{contrato.DataFim}' | Posição: 133-140 | Status: {(validacaoDataFim.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoDataFim.erro ?? "Nenhum"}");

            if (!validacaoDataFim.valido) return null;

            contrato.DataFimFormatada = validacaoDataFim.dataFormatada;

            // 9. VALOR DO CONTRATO (Posição 141-155)
            contrato.ValorContrato = linha.Substring(140, 15);
            var validacaoValor = ValidadoresUniversais.ValidarValorMonetario(contrato.ValorContrato, 2);

            Console.WriteLine($"[ENTIDADE: {entidade}] valorContrato: '{contrato.ValorContrato}' | Posição: 141-155 | Status: {(validacaoValor.valido ? "VÁLIDO" : "INVÁLIDO")} | Erro: {validacaoValor.erro ?? "Nenhum"}");

            if (!validacaoValor.valido) return null;

            contrato.ValorContratoDecimal = validacaoValor.valorDecimal;
            contrato.ValorContratoFormatado = validacaoValor.valorFormatado;

            // VALIDAÇÃO DE CONSISTÊNCIA ENTRE DATAS
            if (validacaoDataInicio.valido && validacaoDataFim.valido &&
                !validacaoDataInicio.dataNula && !validacaoDataFim.dataNula)
            {
                var dataInicioObj = DateTime.ParseExact(validacaoDataInicio.dataISO, "yyyy-MM-dd", null);
                var dataFimObj = DateTime.ParseExact(validacaoDataFim.dataISO, "yyyy-MM-dd", null);

                if (dataFimObj <= dataInicioObj)
                {
                    Console.WriteLine($"[ENTIDADE: {entidade}] ERRO DE CONSISTÊNCIA: Data de fim ({validacaoDataFim.dataFormatada}) deve ser posterior à data de início ({validacaoDataInicio.dataFormatada})");
                }
            }

            // RESUMO DA EXTRAÇÃO
            var extratoValido = validacaoTipo.Item1 && validacaoGrupo.valido &&
                               validacaoContrato.Item1 && validacaoNome.Item1 &&
                               validacaoStatus.valido && validacaoDataInicio.valido &&
                               validacaoDataFim.valido && validacaoValor.valido;

            Console.WriteLine($"\n[RESUMO ENTIDADE: {entidade}] Status Geral: {(extratoValido ? "SUCESSO" : "FALHA")} | Linha: {numeroLinha} | Contrato: {contrato.NumeroContrato}");

            return extratoValido ? contrato : null;
        }
    }
}
