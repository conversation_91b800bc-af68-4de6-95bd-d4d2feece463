using System;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa um registro de parcela do arquivo PARCELAS_OUT.txt
    /// Layout: 106 caracteres por linha
    /// Código de Registro: "000"
    /// </summary>
    public class RegistroParcelaNova
    {
        // Campos básicos (posições 1-29)
        public string TipoRegistro { get; set; } = string.Empty; // Posição 1-3: "000"
        public string Grupo { get; set; } = string.Empty; // Posição 4: 1=QuintoAndar, 2=QuintoCred
        public string ChaveCyber { get; set; } = string.Empty; // Posição 5-29

        // Dados da parcela (posições 30-106)
        public string IdParcela { get; set; } = string.Empty; // Posição 30-44
        public string StatusParcela { get; set; } = string.Empty; // Posição 45: 1/2/3/4/5/6
        public string ValorPrincipal { get; set; } = string.Empty; // Posição 46-60
        public string ValorJuros { get; set; } = string.Empty; // Posição 61-75
        public string ValorMulta { get; set; } = string.Empty; // Posição 76-90
        public string DataVencimento { get; set; } = string.Empty; // Posição 91-98: MMDDAAAA
        public string DataPagamento { get; set; } = string.Empty; // Posição 99-106: MMDDAAAA

        // Propriedades derivadas
        public string GrupoDescricao => Grupo switch
        {
            "1" => "QuintoAndar - Locação",
            "2" => "QuintoCred - Crédito",
            _ => "Grupo Desconhecido"
        };

        public string StatusDescricao => StatusParcela switch
        {
            "1" => "Ativa (em aberto)",
            "2" => "Paga",
            "3" => "Cancelada",
            "4" => "Pausa",
            "5" => "Previamente Negociada",
            "6" => "Em Negociação",
            _ => "Status Desconhecido"
        };

        // Formatação de valores
        public decimal ValorPrincipalDecimal
        {
            get
            {
                if (decimal.TryParse(ValorPrincipal, out decimal valor))
                {
                    return valor / 100;
                }
                return 0;
            }
        }

        public decimal ValorJurosDecimal
        {
            get
            {
                if (decimal.TryParse(ValorJuros, out decimal valor))
                {
                    return valor / 100;
                }
                return 0;
            }
        }

        public decimal ValorMultaDecimal
        {
            get
            {
                if (decimal.TryParse(ValorMulta, out decimal valor))
                {
                    return valor / 100;
                }
                return 0;
            }
        }

        public decimal ValorTotal => ValorPrincipalDecimal + ValorJurosDecimal + ValorMultaDecimal;

        public string ValorTotalFormatado => ValorTotal.ToString("C2");

        // Formatação de datas
        public string DataVencimentoFormatada
        {
            get
            {
                if (DataVencimento.Length == 8 && DateTime.TryParseExact(DataVencimento, "MMddyyyy", null, System.Globalization.DateTimeStyles.None, out DateTime data))
                {
                    return data.ToString("dd/MM/yyyy");
                }
                return DataVencimento;
            }
        }

        public string DataPagamentoFormatada
        {
            get
            {
                if (DataPagamento.Length == 8 && DateTime.TryParseExact(DataPagamento, "MMddyyyy", null, System.Globalization.DateTimeStyles.None, out DateTime data))
                {
                    return data.ToString("dd/MM/yyyy");
                }
                return DataPagamento;
            }
        }

        // Cálculo de dias de atraso
        public int DiasAtraso
        {
            get
            {
                // Só calcula atraso para parcelas não pagas (status 1 = Pendente)
                if (StatusParcela == "1" && DataVencimento.Length == 8 &&
                    DateTime.TryParseExact(DataVencimento, "MMddyyyy", null, System.Globalization.DateTimeStyles.None, out DateTime dataVenc))
                {
                    var hoje = DateTime.Now.Date;
                    if (dataVenc < hoje)
                    {
                        return (hoje - dataVenc).Days;
                    }
                }
                return 0;
            }
        }

        public string SituacaoAtraso
        {
            get
            {
                // Se status é 2 (Pago) mas não tem data de pagamento, indica inconsistência
                if (StatusParcela == "2" && (DataPagamento == "00000000" || string.IsNullOrWhiteSpace(DataPagamento)))
                {
                    return "PAGO_SEM_DATA";
                }

                // Se status é 2 (Pago) com data válida, está pago
                if (StatusParcela == "2")
                {
                    return "PAGO";
                }

                // Se status é 3 (Cancelado)
                if (StatusParcela == "3")
                {
                    return "CANCELADO";
                }

                // Para status 1 (Pendente), verifica atraso
                return DiasAtraso > 0 ? "EM_ATRASO" : "EM_DIA";
            }
        }
    }
}
