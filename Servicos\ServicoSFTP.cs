using Microsoft.Extensions.Logging;
using Renci.SshNet;
using SistemaMFT.Configuracao;

namespace SistemaMFT.Servicos
{
    /// <summary>
    /// Serviço para operações SFTP - conectar, autenticar, buscar e baixar arquivos
    /// </summary>
    public class ServicoSFTP
    {
        private readonly ConfiguracaoMFT _configuracao;
        private readonly ILogger<ServicoSFTP> _logger;

        public ServicoSFTP(ConfiguracaoMFT configuracao, ILogger<ServicoSFTP> logger)
        {
            _configuracao = configuracao ?? throw new ArgumentNullException(nameof(configuracao));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Conecta ao servidor SFTP e baixa todos os arquivos .txt disponíveis
        /// </summary>
        /// <returns>Lista de arquivos baixados</returns>
        public async Task<List<string>> BaixarArquivosAsync()
        {
            var arquivosBaixados = new List<string>();

            _logger.LogInformation("Iniciando conexão SFTP para {Host}:{Porta}", 
                _configuracao.ServidorSFTP.Host, _configuracao.ServidorSFTP.Porta);

            try
            {
                using (var client = new SftpClient(_configuracao.ServidorSFTP.Host, 
                    _configuracao.ServidorSFTP.Porta, 
                    _configuracao.ServidorSFTP.Usuario, 
                    _configuracao.ServidorSFTP.Senha))
                {
                    // Configurar timeout
                    client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(_configuracao.ServidorSFTP.TimeoutSegundos);

                    // Conectar
                    Console.WriteLine($"🔗 Conectando ao servidor SFTP {_configuracao.ServidorSFTP.Host}:{_configuracao.ServidorSFTP.Porta}...");
                    await Task.Run(() => client.Connect());

                    Console.WriteLine($"✅ Conectado com sucesso! Usuário: {_configuracao.ServidorSFTP.Usuario}");
                    _logger.LogInformation("Conexão SFTP estabelecida com sucesso");

                    // Navegar para o diretório remoto
                    Console.WriteLine($"📁 Navegando para o diretório: {_configuracao.ServidorSFTP.DiretorioRemoto}");
                    await Task.Run(() => client.ChangeDirectory(_configuracao.ServidorSFTP.DiretorioRemoto));

                    // Listar arquivos .txt
                    Console.WriteLine("🔍 Buscando arquivos .txt no servidor...");
                    var arquivosRemotos = await Task.Run(() => client.ListDirectory("."));
                    
                    // Filtrar arquivos .zip.gpg no diretório remoto
                    var arquivosGpg = arquivosRemotos
                        .Where(f => f.IsRegularFile && f.Name.EndsWith(".zip.gpg", StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    if (!arquivosGpg.Any())
                    {
                        Console.WriteLine("⚠️  Nenhum arquivo .zip.gpg encontrado no servidor");
                        _logger.LogWarning("Nenhum arquivo .zip.gpg encontrado no diretório remoto");
                        return arquivosBaixados;
                    }

                    Console.WriteLine($"📄 Encontrados {arquivosGpg.Count} arquivo(s) .zip.gpg:");
                    foreach (var arquivo in arquivosGpg)
                    {
                        Console.WriteLine($"   • {arquivo.Name} ({arquivo.Length} bytes)");
                    }

                    // Garantir que diretório Downloads existe no diretório do projeto
                    var diretorioDownload = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Downloads");
                    if (!Directory.Exists(diretorioDownload))
                        Directory.CreateDirectory(diretorioDownload);

                    // Baixar cada arquivo
                    foreach (var arquivo in arquivosGpg)
                    {
                        var nomeArquivo = arquivo.Name;
                        var caminhoLocal = Path.Combine(diretorioDownload, nomeArquivo);

                        Console.WriteLine($"⬇️  Baixando: {nomeArquivo}...");
                        
                        try
                        {
                            using (var fileStream = new FileStream(caminhoLocal, FileMode.Create))
                            {
                                await Task.Run(() => client.DownloadFile(nomeArquivo, fileStream));
                            }

                            arquivosBaixados.Add(caminhoLocal);
                            Console.WriteLine($"✅ {nomeArquivo} baixado com sucesso!");
                            _logger.LogInformation("Arquivo baixado: {Arquivo}", nomeArquivo);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"❌ Erro ao baixar {nomeArquivo}: {ex.Message}");
                            _logger.LogError(ex, "Erro ao baixar arquivo: {Arquivo}", nomeArquivo);
                        }
                    }

                    // Desconectar
                    client.Disconnect();
                    Console.WriteLine("🔌 Desconectado do servidor SFTP");
                    _logger.LogInformation("Conexão SFTP encerrada");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erro na conexão SFTP: {ex.Message}");
                _logger.LogError(ex, "Erro na conexão SFTP");
                throw;
            }

            return arquivosBaixados;
        }

        /// <summary>
        /// Testa a conexão SFTP sem baixar arquivos
        /// </summary>
        /// <returns>True se conexão foi bem-sucedida</returns>
        public async Task<bool> TestarConexaoAsync()
        {
            _logger.LogInformation("Testando conexão SFTP...");

            try
            {
                using (var client = new SftpClient(_configuracao.ServidorSFTP.Host, 
                    _configuracao.ServidorSFTP.Porta, 
                    _configuracao.ServidorSFTP.Usuario, 
                    _configuracao.ServidorSFTP.Senha))
                {
                    client.ConnectionInfo.Timeout = TimeSpan.FromSeconds(_configuracao.ServidorSFTP.TimeoutSegundos);

                    Console.WriteLine($"🔗 Testando conexão SFTP {_configuracao.ServidorSFTP.Host}:{_configuracao.ServidorSFTP.Porta}...");
                    await Task.Run(() => client.Connect());

                    Console.WriteLine($"✅ Conexão SFTP OK! Usuário: {_configuracao.ServidorSFTP.Usuario}");
                    Console.WriteLine($"📁 Diretório remoto: {_configuracao.ServidorSFTP.DiretorioRemoto}");

                    // Testar navegação para o diretório
                    await Task.Run(() => client.ChangeDirectory(_configuracao.ServidorSFTP.DiretorioRemoto));
                    Console.WriteLine($"✅ Diretório remoto acessível");

                    // Listar conteúdo
                    var arquivos = await Task.Run(() => client.ListDirectory("."));
                    var contadorTxt = arquivos.Count(f => f.IsRegularFile && f.Name.EndsWith(".txt", StringComparison.OrdinalIgnoreCase));
                    Console.WriteLine($"📄 Arquivos .txt disponíveis: {contadorTxt}");

                    client.Disconnect();
                    Console.WriteLine("🔌 Teste concluído com sucesso!");

                    _logger.LogInformation("Teste de conexão SFTP bem-sucedido");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Falha no teste de conexão: {ex.Message}");
                _logger.LogError(ex, "Falha no teste de conexão SFTP");
                return false;
            }
        }
    }
} 