//------------------------------------------------------------------------------
// <auto-generated>
//     O código foi gerado por uma ferramenta.
//     Versão de Tempo de Execução:4.0.30319.42000
//
//     As alterações ao arquivo poderão causar comportamento incorreto e serão perdidas se
//     o código for gerado novamente.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("SistemaMFT")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © 2024 QuintoAndar")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("1.0.0.0")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("Sistema MFT QuintoAndar")]
[assembly: System.Reflection.AssemblyTitleAttribute("Sistema MFT - Processamento de Arquivos e APIs CyberAgreements")]
[assembly: System.Reflection.AssemblyVersionAttribute("1.0.0.0")]

// Gerado pela classe WriteCodeFragment do MSBuild.

