using System;
using System.IO;
using System.Threading.Tasks;
using SistemaMFT.Core.Interfaces;
using SistemaMFT.Core.Entities;
using SistemaMFT.Core.Common;
using SistemaMFT.Modules.Parcelas.Entities;
using SistemaMFT.Modules.Parcelas.Validators;

namespace SistemaMFT.Modules.Parcelas.Processors
{
    /// <summary>
    /// Processador específico para arquivos de parcelas
    /// Implementa IFileProcessor para processar arquivos PARCELAS_OUT.txt
    /// Suporta processamento adaptativo para linhas de 228 ou 1653/1649 caracteres
    /// Mantém protocolo de logging original para compatibilidade
    /// </summary>
    public class ParcelasProcessor : IFileProcessor<RegistroParcela>
    {
        private readonly ParcelasValidator _validator;

        /// <summary>
        /// Construtor do processador de parcelas
        /// </summary>
        public ParcelasProcessor()
        {
            _validator = new ParcelasValidator();
        }

        /// <summary>
        /// Processa uma linha do arquivo de parcelas
        /// </summary>
        /// <param name="linha"><PERSON>ha a ser processada</param>
        /// <param name="numeroLinha">Número da linha no arquivo</param>
        /// <returns>Resultado do processamento com a parcela extraída</returns>
        public ProcessingResult<RegistroParcela> ProcessarLinha(string linha, int numeroLinha)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(linha))
                {
                    return ProcessingResult<RegistroParcela>.Erro(new[] { $"Linha {numeroLinha} está vazia" }, numeroLinha, linha);
                }

                // Detectar e ajustar formato da linha se necessário
                var linhaProcessada = DetectarEAjustarFormato(linha, numeroLinha);

                // Extrair dados da linha
                var parcela = ExtrairDadosLinha(linhaProcessada, numeroLinha);

                // Validar dados extraídos
                var resultado = ValidarDados(parcela);

                // Atualizar propriedades derivadas
                parcela.AtualizarPropriedadesDerivadas();

                // Logar campos extraídos (mantém protocolo original)
                LogarCamposExtraidos(parcela);

                var resultadoProcessamento = ProcessingResult<RegistroParcela>.Sucesso(parcela, numeroLinha, linha);

                if (!resultado.IsValid)
                {
                    foreach (var erro in resultado.Erros)
                    {
                        resultadoProcessamento.AdicionarErro(erro);
                    }
                }

                return resultadoProcessamento;
            }
            catch (Exception ex)
            {
                return ProcessingResult<RegistroParcela>.Erro(new[] { $"Erro ao processar linha {numeroLinha}: {ex.Message}" }, numeroLinha, linha);
            }
        }

        /// <summary>
        /// Processa um arquivo completo de parcelas
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Resultado do processamento do arquivo</returns>
        public async Task<FileProcessingResult<RegistroParcela>> ProcessarArquivoAsync(string caminhoArquivo)
        {
            var resultado = new FileProcessingResult<RegistroParcela>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                if (!File.Exists(caminhoArquivo))
                {
                    Console.WriteLine($"[ERRO] Arquivo não encontrado: {caminhoArquivo}");
                    resultado.ProcessadoComSucesso = false;
                    return resultado;
                }

                Console.WriteLine($"[PARCELAS] Iniciando processamento do arquivo: {resultado.NomeArquivo}");

                var linhas = await File.ReadAllLinesAsync(caminhoArquivo);
                int numeroLinha = 0;

                foreach (var linha in linhas)
                {
                    numeroLinha++;

                    var resultadoLinha = ProcessarLinha(linha, numeroLinha);

                    if (resultadoLinha.IsValid && resultadoLinha.Registro != null)
                    {
                        resultado.RegistrosValidos.Add(resultadoLinha.Registro);
                    }
                    else
                    {
                        resultado.RegistrosComErro.Add(resultadoLinha);
                        Console.WriteLine($"[ERRO] Linha {numeroLinha}: {string.Join(", ", resultadoLinha.Erros)}");
                    }
                }

                resultado.ProcessadoComSucesso = true;
                Console.WriteLine($"[PARCELAS] Processamento concluído. Válidos: {resultado.TotalValidos}, Erros: {resultado.TotalErros}");

                return resultado;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERRO] Erro ao processar arquivo: {ex.Message}");
                resultado.ProcessadoComSucesso = false;
                return resultado;
            }
        }

        /// <summary>
        /// Valida o formato do arquivo de parcelas
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>True se o formato é válido</returns>
        public bool ValidarFormatoArquivo(string caminhoArquivo)
        {
            try
            {
                if (!File.Exists(caminhoArquivo))
                    return false;

                var nomeArquivo = Path.GetFileName(caminhoArquivo).ToUpper();
                return nomeArquivo.Contains("PARCELAS") || nomeArquivo.Contains("PARCELA");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Obtém informações sobre o tipo de arquivo de parcelas
        /// </summary>
        /// <returns>Metadados do tipo de arquivo</returns>
        public FileTypeInfo ObterTipoArquivo()
        {
            return new FileTypeInfo
            {
                TipoArquivo = "PARCELAS",
                CodigoRegistro = "000",
                PadraoNome = "*PARCELAS*",
                TamanhoLinha = 228,
                ExtensoesSuportadas = new[] { ".txt" },
                Descricao = "Arquivo de parcelas com dados de vencimento e pagamento"
            };
        }

        #region Métodos Privados

        /// <summary>
        /// Detecta e ajusta o formato da linha se necessário
        /// Suporta linhas de 228 caracteres (padrão) e 1653/1649 caracteres (estendido)
        /// </summary>
        private string DetectarEAjustarFormato(string linha, int numeroLinha)
        {
            var tamanhoLinha = linha.Length;

            // Formato padrão (228 caracteres)
            if (tamanhoLinha == 228)
            {
                return linha;
            }

            // Formato estendido (1653 ou 1649 caracteres) - truncar para 228
            if (tamanhoLinha >= 1649)
            {
                Console.WriteLine($"[PARCELAS] Linha {numeroLinha}: Detectado formato estendido ({tamanhoLinha} chars), truncando para 228 chars");
                return linha.Substring(0, 228);
            }

            // Linha menor que esperado - usar como está
            if (tamanhoLinha < 228)
            {
                Console.WriteLine($"[PARCELAS] Linha {numeroLinha}: Linha menor que esperado ({tamanhoLinha} chars), processando como está");
                return linha;
            }

            return linha;
        }

        /// <summary>
        /// Extrai os dados de uma linha formatada
        /// </summary>
        private RegistroParcela ExtrairDadosLinha(string linha, int numeroLinha)
        {
            var parcela = new RegistroParcela
            {
                NumeroLinha = numeroLinha,
                LinhaOriginal = linha
            };

            // Extrair campos conforme layout de 228 caracteres
            parcela.TipoRegistro = StringHelper.ExtrairSubstring(linha, 0, 3);           // Posições 1-3
            parcela.Grupo = StringHelper.ExtrairSubstring(linha, 3, 1);                  // Posição 4
            parcela.NumeroContrato = StringHelper.LimparEspacos(StringHelper.ExtrairSubstring(linha, 4, 25));    // Posições 5-29
            parcela.NumeroParcela = StringHelper.ExtrairSubstring(linha, 29, 3);         // Posições 30-32
            parcela.DataVencimento = StringHelper.ExtrairSubstring(linha, 32, 8);        // Posições 33-40
            parcela.ValorPrincipal = StringHelper.ExtrairSubstring(linha, 40, 15);       // Posições 41-55
            parcela.ValorJuros = StringHelper.ExtrairSubstring(linha, 55, 15);           // Posições 56-70
            parcela.ValorMulta = StringHelper.ExtrairSubstring(linha, 70, 15);           // Posições 71-85
            parcela.StatusParcela = StringHelper.ExtrairSubstring(linha, 85, 1);         // Posição 86
            parcela.DataPagamento = StringHelper.ExtrairSubstring(linha, 86, 8);         // Posições 87-94
            parcela.ValorPago = StringHelper.ExtrairSubstring(linha, 94, 15);            // Posições 95-109

            // Ranking está nas posições 225-228 (últimas 4 posições)
            if (linha.Length >= 228)
            {
                parcela.Ranking = StringHelper.ExtrairSubstring(linha, 224, 4);          // Posições 225-228
            }

            return parcela;
        }

        /// <summary>
        /// Valida os dados extraídos da parcela
        /// </summary>
        private ValidationResult ValidarDados(RegistroParcela parcela)
        {
            var resultado = new ValidationResult();

            // 1. TIPO DE REGISTRO
            var validacaoTipo = _validator.ValidarCampo(parcela.TipoRegistro, "tiporegistro");
            LogarValidacao("tipoRegistro", parcela.TipoRegistro, "1-3", validacaoTipo);
            if (!validacaoTipo.IsValid) resultado.Erros.Add(validacaoTipo.Erro);

            // 2. GRUPO
            var validacaoGrupo = _validator.ValidarCampo(parcela.Grupo, "grupo");
            LogarValidacao("grupo", parcela.Grupo, "4", validacaoGrupo);
            if (!validacaoGrupo.IsValid) resultado.Erros.Add(validacaoGrupo.Erro);

            // 3. NÚMERO DO CONTRATO
            var validacaoContrato = _validator.ValidarCampo(parcela.NumeroContrato, "numerocontrato");
            LogarValidacao("numeroContrato", parcela.NumeroContrato, "5-29", validacaoContrato);
            if (!validacaoContrato.IsValid) resultado.Erros.Add(validacaoContrato.Erro);

            // 4. NÚMERO DA PARCELA
            var validacaoParcela = _validator.ValidarCampo(parcela.NumeroParcela, "numeroparcela");
            LogarValidacao("numeroParcela", parcela.NumeroParcela, "30-32", validacaoParcela);
            if (!validacaoParcela.IsValid) resultado.Erros.Add(validacaoParcela.Erro);

            // 5. DATA DE VENCIMENTO
            var validacaoDataVenc = _validator.ValidarCampo(parcela.DataVencimento, "datavencimento");
            LogarValidacao("dataVencimento", parcela.DataVencimento, "33-40", validacaoDataVenc);
            if (!validacaoDataVenc.IsValid) resultado.Erros.Add(validacaoDataVenc.Erro);

            // 6. VALOR PRINCIPAL
            var validacaoValorPrincipal = _validator.ValidarCampo(parcela.ValorPrincipal, "valorprincipal");
            LogarValidacao("valorPrincipal", parcela.ValorPrincipal, "41-55", validacaoValorPrincipal);
            if (!validacaoValorPrincipal.IsValid) resultado.Erros.Add(validacaoValorPrincipal.Erro);

            // 7. VALOR DE JUROS (opcional)
            var validacaoValorJuros = _validator.ValidarCampo(parcela.ValorJuros, "valorjuros");
            LogarValidacao("valorJuros", parcela.ValorJuros, "56-70", validacaoValorJuros);
            if (!validacaoValorJuros.IsValid) resultado.Erros.Add(validacaoValorJuros.Erro);

            // 8. VALOR DE MULTA (opcional)
            var validacaoValorMulta = _validator.ValidarCampo(parcela.ValorMulta, "valormulta");
            LogarValidacao("valorMulta", parcela.ValorMulta, "71-85", validacaoValorMulta);
            if (!validacaoValorMulta.IsValid) resultado.Erros.Add(validacaoValorMulta.Erro);

            // 9. STATUS DA PARCELA
            var validacaoStatus = _validator.ValidarCampo(parcela.StatusParcela, "statusparcela");
            LogarValidacao("statusParcela", parcela.StatusParcela, "86", validacaoStatus);
            if (!validacaoStatus.IsValid) resultado.Erros.Add(validacaoStatus.Erro);

            // 10. DATA DE PAGAMENTO (opcional)
            var validacaoDataPag = _validator.ValidarCampo(parcela.DataPagamento, "datapagamento");
            LogarValidacao("dataPagamento", parcela.DataPagamento, "87-94", validacaoDataPag);
            if (!validacaoDataPag.IsValid) resultado.Erros.Add(validacaoDataPag.Erro);

            // 11. VALOR PAGO (opcional)
            var validacaoValorPago = _validator.ValidarCampo(parcela.ValorPago, "valorpago");
            LogarValidacao("valorPago", parcela.ValorPago, "95-109", validacaoValorPago);
            if (!validacaoValorPago.IsValid) resultado.Erros.Add(validacaoValorPago.Erro);

            // 12. RANKING (opcional)
            var validacaoRanking = _validator.ValidarCampo(parcela.Ranking, "ranking");
            LogarValidacao("ranking", parcela.Ranking, "225-228", validacaoRanking);
            if (!validacaoRanking.IsValid) resultado.Erros.Add(validacaoRanking.Erro);

            return resultado;
        }

        /// <summary>
        /// Loga os campos extraídos mantendo o protocolo original
        /// </summary>
        private void LogarCamposExtraidos(RegistroParcela parcela)
        {
            Console.WriteLine($"[PARCELA] Linha {parcela.NumeroLinha}:");
            Console.WriteLine($"  Tipo: {parcela.TipoRegistro}");
            Console.WriteLine($"  Grupo: {parcela.Grupo} ({parcela.GrupoDescricao})");
            Console.WriteLine($"  Contrato: {parcela.NumeroContrato}");
            Console.WriteLine($"  Parcela: {parcela.NumeroParcela}");
            Console.WriteLine($"  Vencimento: {parcela.DataVencimento} ({parcela.DataVencimentoFormatada})");
            Console.WriteLine($"  Valor Principal: {parcela.ValorPrincipal} (R$ {parcela.ValorPrincipalDecimal:F2})");
            Console.WriteLine($"  Valor Juros: {parcela.ValorJuros} (R$ {parcela.ValorJurosDecimal:F2})");
            Console.WriteLine($"  Valor Multa: {parcela.ValorMulta} (R$ {parcela.ValorMultaDecimal:F2})");
            Console.WriteLine($"  Valor Total: R$ {parcela.ValorTotal:F2}");
            Console.WriteLine($"  Status: {parcela.StatusParcela} ({parcela.StatusDescricao})");
            Console.WriteLine($"  Data Pagamento: {parcela.DataPagamento} ({parcela.DataPagamentoFormatada})");
            Console.WriteLine($"  Valor Pago: {parcela.ValorPago} (R$ {parcela.ValorPagoDecimal:F2})");
            Console.WriteLine($"  Ranking: {parcela.Ranking} ({parcela.RankingNumerico})");
            Console.WriteLine($"  Situação: {parcela.SituacaoAtraso}");
            if (parcela.DiasAtraso > 0)
            {
                Console.WriteLine($"  Dias Atraso: {parcela.DiasAtraso}");
            }
        }

        /// <summary>
        /// Loga resultado de validação de campo específico
        /// </summary>
        private void LogarValidacao(string nomeCampo, string valor, string posicoes, FieldValidationResult validacao)
        {
            var status = validacao.IsValid ? "OK" : "ERRO";
            Console.WriteLine($"[VALIDACAO] {nomeCampo} (pos {posicoes}): '{valor}' - {status}");

            if (!validacao.IsValid)
            {
                Console.WriteLine($"[VALIDACAO] Erro: {validacao.Erro}");
            }
        }

        #endregion
    }
}
