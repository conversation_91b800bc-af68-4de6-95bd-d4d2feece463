# 🚀 Como Executar o Sistema MFT

## ⚡ Execução Rápida

### 1. Pré-requisitos
- **.NET 6.0 SDK** ou superior instalado
- **Windows, Linux ou macOS**

### 2. Comandos para Executar

```bash
# 1. Navegar para o diretório do projeto
cd SistemaMFT

# 2. Restaurar dependências
dotnet restore

# 3. Compilar o projeto
dotnet build

# 4. Executar o sistema
dotnet run
```

## 📋 Preparação Inicial

### 1. Criar Diretórios de Trabalho
```bash
mkdir -p Arquivos/Entrada
mkdir -p Arquivos/<PERSON><PERSON>
mkdir -p Arquivos/Processados
mkdir -p Arquivos/Erros
```

### 2. Colocar Arquivos de Teste
Coloque arquivos `.txt` de teste no diretório `Arquivos/Entrada/`:
- `CONTRATOS_OUT_20241201.txt`
- `PARCELAS_OUT_20241201.txt`
- `RELATIONS_OUT_20241201.txt`
- `ACORDOS_OUT_20241201.txt`

### 3. Verificar Configuração
Edite o arquivo `appsettings.json` se necessário (as credenciais já estão configuradas).

## 🖥️ Interface do Sistema

Ao executar, você verá:

```
╔═══════════════════════════════════════════════════════════════════════════╗
║                                                                           ║
║                      SISTEMA MFT - QUINTOANDAR                           ║
║                                                                           ║
║              Processamento de Arquivos e APIs CyberAgreements            ║
║                                                                           ║
║                              Versão 1.0.0                               ║
║                                                                           ║
╚═══════════════════════════════════════════════════════════════════════════╝

═══════════════════════════════════════════════════════════════
                       MENU PRINCIPAL                          
═══════════════════════════════════════════════════════════════

1. 📁 Processar Arquivos MFT
2. 🔗 Testar Conexões (SFTP/MFT/API)
3. ⚙️  Exibir Configuração
4. 🧹 Limpar Diretórios
5. 📊 Gerar Relatórios
0. ❌ Sair

═══════════════════════════════════════════════════════════════
Digite sua opção:
```

## 🔍 Teste do Sistema

### 1. Processar Arquivos
1. Digite **1** para processar arquivos
2. Escolha **A** para processar todos ou o número de um arquivo específico
3. Acompanhe o progresso na tela

### 2. Ver Configuração
1. Digite **3** para ver as configurações atuais
2. Verifique se todos os parâmetros estão corretos

## 📁 Estrutura de Arquivos Resultante

Após execução, terá:
```
SistemaMFT/
├── Arquivos/
│   ├── Entrada/          # Arquivos para processar (vazio após processamento)
│   ├── Saida/           # Arquivos gerados pelo sistema
│   ├── Processados/     # Arquivos processados com sucesso
│   └── Erros/          # Arquivos que deram erro
├── Configuracao/
│   └── chaves/         # Chaves GPG
├── [outros diretórios do código]
```

## ⚠️ Possíveis Problemas

### Erro: "SDK não encontrado"
```bash
# Instalar .NET 6.0 SDK:
# Windows: Baixar de https://dotnet.microsoft.com/download
# Ubuntu: sudo apt-get install dotnet-sdk-6.0
# macOS: brew install dotnet
```

### Erro: "Arquivo não encontrado"
```bash
# Verificar se está no diretório correto
pwd
ls -la

# Deve mostrar os arquivos .cs e .csproj
```

### Erro: "Acesso negado"
```bash
# Linux/macOS: Dar permissões
chmod +x *
sudo chmod 755 Arquivos/ -R
```

## 🎯 Próximos Passos

1. **✅ Sistema Executando**: Comece processando arquivos de teste
2. **🔧 Configurar Produção**: Ajuste credenciais reais no `appsettings.json`
3. **📡 Implementar SFTP**: Conectar aos servidores reais
4. **🔐 Configurar GPG**: Testar criptografia/descriptografia
5. **🌐 APIs**: Implementar integração completa com CyberAgreements

---

**✨ Pronto! O sistema está funcionando e pronto para processar arquivos MFT.** 