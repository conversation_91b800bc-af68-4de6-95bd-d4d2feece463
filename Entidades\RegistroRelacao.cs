using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa um registro do arquivo RELATIONS_OUT.txt
    /// Layout conforme especificação: 194 caracteres por linha
    /// </summary>
    public class RegistroRelacao
    {
        /// <summary>
        /// Tipo de registro - posições 1-3 (valores em branco)
        /// </summary>
        [StringLength(3)]
        public string TipoRegistro { get; set; } = "   ";

        /// <summary>
        /// Grupo - posição 4 (1=QuintoAndar, 2=QuintoCred)
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Grupo { get; set; } = string.Empty;

        /// <summary>
        /// Número do contrato - posições 5-29 (25 caracteres)
        /// </summary>
        [Required]
        [StringLength(25)]
        public string NumeroContrato { get; set; } = string.Empty;

        /// <summary>
        /// Código da relação - posições 30-31 (2 caracteres)
        /// 8=Avalista, 10=Fiador, 12=Garantidor, 20=Participante
        /// </summary>
        [Required]
        [StringLength(2)]
        public string CodigoRelacao { get; set; } = string.Empty;

        /// <summary>
        /// Saudação - posições 32-34 (3 caracteres)
        /// 1=Sr., 2=Sra.
        /// </summary>
        [Required]
        [StringLength(3)]
        public string Saudacao { get; set; } = string.Empty;

        /// <summary>
        /// Nome do avalista/fiador/garantidor - posições 35-114 (80 caracteres)
        /// </summary>
        [Required]
        [StringLength(80)]
        public string Nome { get; set; } = string.Empty;

        /// <summary>
        /// Informação adicional - posições 115-194 (80 caracteres)
        /// </summary>
        [Required]
        [StringLength(80)]
        public string InformacaoAdicional { get; set; } = string.Empty;

        /// <summary>
        /// Obtém a descrição do tipo de relação
        /// </summary>
        /// <returns>Descrição do tipo de relação</returns>
        public string ObterDescricaoRelacao()
        {
            return CodigoRelacao switch
            {
                "8" => "Avalista",
                "10" => "Fiador",
                "12" => "Garantidor",
                "20" => "Participante",
                _ => "Relação Desconhecida"
            };
        }

        /// <summary>
        /// Obtém a descrição da saudação
        /// </summary>
        /// <returns>Descrição da saudação</returns>
        public string ObterDescricaoSaudacao()
        {
            return Saudacao switch
            {
                "1" => "Sr.",
                "2" => "Sra.",
                _ => "Não informado"
            };
        }

        /// <summary>
        /// Obtém o nome completo formatado com saudação
        /// </summary>
        /// <returns>Nome completo formatado</returns>
        public string ObterNomeCompleto()
        {
            var saudacao = ObterDescricaoSaudacao();
            if (saudacao == "Não informado")
                return Nome.Trim();
            
            return $"{saudacao} {Nome.Trim()}";
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras mínimas
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return (Grupo == "1" || Grupo == "2") &&
                   !string.IsNullOrEmpty(NumeroContrato) &&
                   new[] { "8", "10", "12", "20" }.Contains(CodigoRelacao) &&
                   new[] { "1", "2" }.Contains(Saudacao) &&
                   !string.IsNullOrEmpty(Nome);
        }
    }
} 