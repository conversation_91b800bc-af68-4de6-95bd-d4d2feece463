using System;
using System.Collections.Generic;
using System.Linq;
using SistemaMFT.Entidades;
using SistemaMFT.Utilitarios;

namespace SistemaMFT.Servicos
{
    /// <summary>
    /// Processador para arquivos de telefones (TELEFONES_OUT.txt)
    /// Layout: 77 caracteres por linha
    /// Código de registro: "701"
    /// </summary>
    public static class ProcessadorTelefones
    {
        /// <summary>
        /// Processa uma linha do arquivo de telefones
        /// </summary>
        /// <param name="linha">Linha a ser processada (77 caracteres)</param>
        /// <returns>Tupla com resultado da validação e registro processado</returns>
        public static (bool IsValid, RegistroTelefone Registro, string[] Erros) ProcessarLinhaTelefone(string linha)
        {
            var erros = new List<string>();
            var registro = new RegistroTelefone();

            // Verifica se a linha está vazia
            if (string.IsNullOrEmpty(linha))
            {
                erros.Add("Linha vazia ou nula");
                return (false, registro, erros.ToArray());
            }

            // Detecta formato e processa adequadamente
            if (linha.Length == 77)
            {
                // Formato original esperado
                return ProcessarLinhaFormatoOriginalTelefone(linha);
            }
            else if (linha.Length > 77)
            {
                // Formato alternativo com linha maior - trunca para 77 caracteres
                Console.WriteLine($"AVISO: Linha com {linha.Length} caracteres detectada. Tentando processar como formato alternativo...");
                Console.WriteLine($"AVISO: Truncando linha de {linha.Length} para 77 caracteres");
                return ProcessarLinhaFormatoAlternativoTelefone(linha);
            }
            else
            {
                erros.Add($"Linha deve ter pelo menos 77 caracteres. Tamanho atual: {linha.Length}");
                return (false, registro, erros.ToArray());
            }

        }

        /// <summary>
        /// Processa linha no formato original de 77 caracteres
        /// </summary>
        private static (bool IsValid, RegistroTelefone Registro, string[] Erros) ProcessarLinhaFormatoOriginalTelefone(string linha)
        {
            var erros = new List<string>();
            var registro = new RegistroTelefone();

            try
            {
                // Extração dos campos conforme layout original
                registro.TipoRegistro = linha.Substring(0, 3).Trim(); // Posições 1-3
                registro.Grupo = linha.Substring(3, 1).Trim(); // Posição 4
                registro.NumeroContrato = linha.Substring(4, 25).Trim(); // Posições 5-29
                registro.CodigoArea = linha.Substring(29, 3).Trim(); // Posições 30-32
                registro.NumeroTelefone = linha.Substring(32, 15).Trim(); // Posições 33-47
                registro.TipoTelefone = linha.Substring(47, 1).Trim(); // Posição 48
                registro.Extensao = linha.Substring(48, 10).Trim(); // Posições 49-58
                registro.DataCriacao = linha.Substring(58, 8).Trim(); // Posições 59-66
                registro.StatusTelefone = linha.Substring(66, 1).Trim(); // Posição 67
                registro.Ranking = linha.Substring(74, 3).Trim(); // Posições 75-77

                return ProcessarValidacoesTelefone(registro, erros);
            }
            catch (Exception ex)
            {
                erros.Add($"Erro ao extrair campos: {ex.Message}");
                return (false, registro, erros.ToArray());
            }
        }

        /// <summary>
        /// Processa linha no formato alternativo (maior que 77 caracteres) truncando para 77
        /// </summary>
        private static (bool IsValid, RegistroTelefone Registro, string[] Erros) ProcessarLinhaFormatoAlternativoTelefone(string linha)
        {
            var erros = new List<string>();
            var registro = new RegistroTelefone();

            try
            {
                // Trunca a linha para 77 caracteres
                string linhaTruncada = linha.Substring(0, 77);

                // Extração dos campos conforme layout original
                registro.TipoRegistro = linhaTruncada.Substring(0, 3).Trim(); // Posições 1-3
                registro.Grupo = linhaTruncada.Substring(3, 1).Trim(); // Posição 4
                registro.NumeroContrato = linhaTruncada.Substring(4, 25).Trim(); // Posições 5-29
                registro.CodigoArea = linhaTruncada.Substring(29, 3).Trim(); // Posições 30-32
                registro.NumeroTelefone = linhaTruncada.Substring(32, 15).Trim(); // Posições 33-47
                registro.TipoTelefone = linhaTruncada.Substring(47, 1).Trim(); // Posição 48
                registro.Extensao = linhaTruncada.Substring(48, 10).Trim(); // Posições 49-58
                registro.DataCriacao = linhaTruncada.Substring(58, 8).Trim(); // Posições 59-66
                registro.StatusTelefone = linhaTruncada.Substring(66, 1).Trim(); // Posição 67
                registro.Ranking = linhaTruncada.Substring(74, 3).Trim(); // Posições 75-77

                return ProcessarValidacoesTelefone(registro, erros);
            }
            catch (Exception ex)
            {
                erros.Add($"Erro ao extrair campos do formato alternativo: {ex.Message}");
                return (false, registro, erros.ToArray());
            }
        }

        /// <summary>
        /// Processa validações e logs para telefone
        /// </summary>
        private static (bool IsValid, RegistroTelefone Registro, string[] Erros) ProcessarValidacoesTelefone(RegistroTelefone registro, List<string> erros)
        {
            try
            {
                // Log dos campos extraídos
                Console.WriteLine($"TELEFONE_CONTATO.TIPO_REGISTRO: {registro.TipoRegistro}");
                Console.WriteLine($"TELEFONE_CONTATO.GRUPO: {registro.Grupo}");
                Console.WriteLine($"TELEFONE_CONTATO.NUMERO_CONTRATO: {registro.NumeroContrato}");
                Console.WriteLine($"TELEFONE_CONTATO.CODIGO_AREA: {registro.CodigoArea}");
                Console.WriteLine($"TELEFONE_CONTATO.NUMERO_TELEFONE: {registro.NumeroTelefone}");
                Console.WriteLine($"TELEFONE_CONTATO.TIPO_TELEFONE: {registro.TipoTelefone}");
                Console.WriteLine($"TELEFONE_CONTATO.EXTENSAO: {registro.Extensao}");
                Console.WriteLine($"TELEFONE_CONTATO.DATA_CRIACAO: {registro.DataCriacao}");
                Console.WriteLine($"TELEFONE_CONTATO.STATUS_TELEFONE: {registro.StatusTelefone}");
                Console.WriteLine($"TELEFONE_CONTATO.RANKING: {registro.Ranking}");

                // Validações
                ValidarTipoRegistro(registro.TipoRegistro, erros);
                ValidarGrupo(registro.Grupo, erros);
                ValidarNumeroContrato(registro.NumeroContrato, erros);
                ValidarCodigoArea(registro.CodigoArea, erros);
                ValidarNumeroTelefone(registro.NumeroTelefone, erros);
                ValidarTipoTelefone(registro.TipoTelefone, erros);
                ValidarDataCriacao(registro.DataCriacao, erros);
                ValidarStatusTelefone(registro.StatusTelefone, erros);
                ValidarRanking(registro.Ranking, erros);

                return (erros.Count == 0, registro, erros.ToArray());
            }
            catch (Exception ex)
            {
                erros.Add($"Erro ao processar linha: {ex.Message}");
                return (false, registro, erros.ToArray());
            }
        }

        private static void ValidarTipoRegistro(string tipoRegistro, List<string> erros)
        {
            if (tipoRegistro != "701")
            {
                erros.Add($"Tipo de registro deve ser '701'. Valor encontrado: '{tipoRegistro}'");
            }
        }

        private static void ValidarGrupo(string grupo, List<string> erros)
        {
            var gruposPermitidos = new[] { "1", "2" };
            var resultado = ValidadoresUniversais.ValidarStatus(grupo, gruposPermitidos, new Dictionary<string, string>
            {
                { "1", "QuintoAndar - Locação" },
                { "2", "QuintoCred - Crédito" }
            });

            if (!resultado.valido)
            {
                erros.Add($"Grupo inválido: {resultado.erro}");
            }
        }

        private static void ValidarNumeroContrato(string numeroContrato, List<string> erros)
        {
            if (string.IsNullOrWhiteSpace(numeroContrato))
            {
                erros.Add("Número do contrato não pode estar vazio");
            }
        }

        private static void ValidarCodigoArea(string codigoArea, List<string> erros)
        {
            var resultado = ValidadoresUniversais.ValidarCodigoArea(codigoArea);
            if (!resultado.valido)
            {
                erros.Add($"Código de área inválido: {resultado.erro}");
            }
        }

        private static void ValidarNumeroTelefone(string numeroTelefone, List<string> erros)
        {
            var resultado = ValidadoresUniversais.ValidarNumeroTelefone(numeroTelefone);
            if (!resultado.valido)
            {
                erros.Add($"Número de telefone inválido: {resultado.erro}");
            }
        }

        private static void ValidarTipoTelefone(string tipoTelefone, List<string> erros)
        {
            var tiposPermitidos = new[] { "0", "1", "2", "4", "5" };
            var descricoes = new Dictionary<string, string>
            {
                { "0", "Outro" },
                { "1", "Casa" },
                { "2", "Trabalho" },
                { "4", "Familiar" },
                { "5", "Celular" }
            };

            var resultado = ValidadoresUniversais.ValidarStatus(tipoTelefone, tiposPermitidos, descricoes);
            if (!resultado.valido)
            {
                erros.Add($"Tipo de telefone inválido: {resultado.erro}");
            }
        }

        private static void ValidarDataCriacao(string dataCriacao, List<string> erros)
        {
            var resultado = ValidadoresUniversais.ValidarDataMMDDAAAA(dataCriacao);
            if (!resultado.valido)
            {
                erros.Add($"Data de criação inválida: {resultado.erro}");
            }
        }

        private static void ValidarStatusTelefone(string statusTelefone, List<string> erros)
        {
            var statusPermitidos = new[] { "A", "I" };
            var descricoes = new Dictionary<string, string>
            {
                { "A", "Ativo" },
                { "I", "Inativo" }
            };

            var resultado = ValidadoresUniversais.ValidarStatus(statusTelefone, statusPermitidos, descricoes);
            if (!resultado.valido)
            {
                erros.Add($"Status do telefone inválido: {resultado.erro}");
            }
        }

        private static void ValidarRanking(string ranking, List<string> erros)
        {
            var resultado = ValidadoresUniversais.ValidarRanking(ranking);
            if (!resultado.valido)
            {
                erros.Add($"Ranking inválido: {resultado.erro}");
            }
        }
    }
}
