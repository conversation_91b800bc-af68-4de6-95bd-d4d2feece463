using System;
using System.Collections.Generic;
using System.Linq;
using SistemaMFT.Core.Interfaces;
using SistemaMFT.Core.Common;
using SistemaMFT.Modules.Telefones.Entities;

namespace SistemaMFT.Modules.Telefones.Validators
{
    /// <summary>
    /// Validador específico para registros de telefones
    /// Implementa todas as regras de validação conforme documentação
    /// Independente de outros módulos, contém apenas lógica específica de telefones
    /// </summary>
    public class TelefonesValidator : IValidator<RegistroTelefone>
    {
        /// <summary>
        /// Valida um registro de telefone completo
        /// Aplica todas as regras de negócio específicas para telefones
        /// </summary>
        /// <param name="telefone">Registro de telefone a ser validado</param>
        /// <returns>Resultado da validação com erros e avisos</returns>
        public ValidationResult Validar(RegistroTelefone telefone)
        {
            var resultado = new ValidationResult();

            if (telefone == null)
            {
                resultado.Erros.Add("Registro de telefone não pode ser nulo");
                return resultado;
            }

            // Validação do tipo de registro
            ValidarTipoRegistro(telefone.TipoRegistro, resultado);

            // Validação do grupo
            ValidarGrupo(telefone.Grupo, resultado);

            // Validação do número do contrato
            ValidarNumeroContrato(telefone.NumeroContrato, resultado);

            // Validação do código de área
            ValidarCodigoArea(telefone.CodigoArea, resultado);

            // Validação do número do telefone
            ValidarNumeroTelefone(telefone.NumeroTelefone, resultado);

            // Validação do tipo de telefone
            ValidarTipoTelefone(telefone.TipoTelefone, resultado);

            // Validação da extensão (opcional)
            ValidarExtensao(telefone.Extensao, resultado);

            // Validação da data de criação
            ValidarDataCriacao(telefone.DataCriacaoTelefone, resultado);

            // Validação do status
            ValidarStatusTelefone(telefone.StatusTelefone, resultado);

            // Validação do ranking
            ValidarRanking(telefone.Ranking, resultado);

            // Validações de consistência
            ValidarConsistenciaGeral(telefone, resultado);

            resultado.IsValid = resultado.Erros.Count == 0;
            return resultado;
        }

        /// <summary>
        /// Valida um campo específico do registro de telefone
        /// </summary>
        /// <param name="valor">Valor do campo</param>
        /// <param name="nomeCampo">Nome do campo</param>
        /// <returns>Resultado da validação do campo</returns>
        public FieldValidationResult ValidarCampo(string valor, string nomeCampo)
        {
            var resultado = new FieldValidationResult();

            switch (nomeCampo.ToLowerInvariant())
            {
                case "tiporegistro":
                    resultado.IsValid = valor == "701";
                    resultado.Erro = resultado.IsValid ? string.Empty : "Tipo de registro deve ser '701'";
                    resultado.ValorFormatado = valor;
                    break;

                case "grupo":
                    resultado.IsValid = ValidationHelper.ValidarValoresPermitidos(valor, "1", "2");
                    resultado.Erro = resultado.IsValid ? string.Empty : "Grupo deve ser '1' (Locação) ou '2' (Crédito)";
                    resultado.ValorFormatado = valor == "1" ? "QuintoAndar - Locação" : 
                                             valor == "2" ? "QuintoCred - Crédito" : valor;
                    break;

                case "codigoarea":
                    var validacaoArea = ValidationHelper.ValidarCodigoArea(valor);
                    resultado.IsValid = validacaoArea.valido;
                    resultado.Erro = validacaoArea.erro;
                    resultado.ValorFormatado = validacaoArea.codigoArea;
                    break;

                case "numerotelefone":
                    var validacaoNumero = ValidationHelper.ValidarNumeroTelefone(valor);
                    resultado.IsValid = validacaoNumero.valido;
                    resultado.Erro = validacaoNumero.erro;
                    resultado.ValorFormatado = validacaoNumero.numero;
                    break;

                case "tipotelefone":
                    resultado.IsValid = ValidationHelper.ValidarValoresPermitidos(valor, "0", "1", "2", "4", "5");
                    resultado.Erro = resultado.IsValid ? string.Empty : "Tipo deve ser '0' (Outro), '1' (Casa), '2' (Trabalho), '4' (Familiar) ou '5' (Celular)";
                    resultado.ValorFormatado = valor switch
                    {
                        "0" => "Outro",
                        "1" => "Casa",
                        "2" => "Trabalho",
                        "4" => "Familiar",
                        "5" => "Celular",
                        _ => valor ?? string.Empty
                    };
                    break;

                case "statustelefone":
                    resultado.IsValid = ValidationHelper.ValidarValoresPermitidos(valor, "A", "I");
                    resultado.Erro = resultado.IsValid ? string.Empty : "Status deve ser 'A' (Ativo) ou 'I' (Inativo)";
                    resultado.ValorFormatado = valor switch
                    {
                        "A" => "Ativo",
                        "I" => "Inativo",
                        _ => valor ?? string.Empty
                    };
                    break;

                case "datacriacao":
                    var validacaoData = ValidationHelper.ValidarDataMMDDAAAA(valor);
                    resultado.IsValid = validacaoData.valido;
                    resultado.Erro = validacaoData.erro;
                    resultado.ValorFormatado = validacaoData.valido ? 
                        validacaoData.dataFormatada?.ToString("dd/MM/yyyy") ?? string.Empty : 
                        valor ?? string.Empty;
                    break;

                case "ranking":
                    var validacaoRanking = ValidationHelper.ValidarRanking(valor);
                    resultado.IsValid = validacaoRanking.valido;
                    resultado.Erro = validacaoRanking.erro;
                    resultado.ValorFormatado = validacaoRanking.ranking;
                    break;

                case "extensao":
                    // Extensão é opcional
                    resultado.IsValid = true;
                    resultado.Erro = string.Empty;
                    resultado.ValorFormatado = StringHelper.LimparEspacos(valor);
                    break;

                default:
                    resultado.IsValid = ValidationHelper.NaoVazio(valor);
                    resultado.Erro = resultado.IsValid ? string.Empty : "Campo não pode estar vazio";
                    resultado.ValorFormatado = StringHelper.LimparEspacos(valor);
                    break;
            }

            return resultado;
        }

        /// <summary>
        /// Obtém todas as regras de validação aplicáveis a telefones
        /// </summary>
        /// <returns>Lista de regras de validação</returns>
        public IEnumerable<ValidationRule> ObterRegrasValidacao()
        {
            return new List<ValidationRule>
            {
                new ValidationRule { Campo = "TipoRegistro", Regra = "Valor fixo", Descricao = "Deve ser sempre '701'" },
                new ValidationRule { Campo = "Grupo", Regra = "Valores permitidos", Descricao = "'1' para Locação ou '2' para Crédito" },
                new ValidationRule { Campo = "NumeroContrato", Regra = "Obrigatório", Descricao = "Não pode estar vazio" },
                new ValidationRule { Campo = "CodigoArea", Regra = "DDD válido", Descricao = "Código de área entre 11 e 99" },
                new ValidationRule { Campo = "NumeroTelefone", Regra = "Telefone válido", Descricao = "8 ou 9 dígitos numéricos" },
                new ValidationRule { Campo = "TipoTelefone", Regra = "Valores permitidos", Descricao = "'0' (Outro), '1' (Casa), '2' (Trabalho), '4' (Familiar), '5' (Celular)" },
                new ValidationRule { Campo = "Extensao", Regra = "Opcional", Descricao = "Ramal do telefone (opcional)" },
                new ValidationRule { Campo = "DataCriacao", Regra = "Data válida", Descricao = "Formato MMDDAAAA com data válida" },
                new ValidationRule { Campo = "StatusTelefone", Regra = "Valores permitidos", Descricao = "'A' (Ativo) ou 'I' (Inativo)" },
                new ValidationRule { Campo = "Ranking", Regra = "Número válido", Descricao = "Valor entre 1 e 999" }
            };
        }

        #region Métodos de Validação Específicos

        private void ValidarTipoRegistro(string tipoRegistro, ValidationResult resultado)
        {
            if (tipoRegistro != "701")
            {
                resultado.Erros.Add($"Tipo de registro inválido: '{tipoRegistro}'. Esperado: '701'");
            }
        }

        private void ValidarGrupo(string grupo, ValidationResult resultado)
        {
            if (!ValidationHelper.ValidarValoresPermitidos(grupo, "1", "2"))
            {
                resultado.Erros.Add($"Grupo inválido: '{grupo}'. Valores permitidos: '1' (Locação), '2' (Crédito)");
            }
        }

        private void ValidarNumeroContrato(string numeroContrato, ValidationResult resultado)
        {
            if (!ValidationHelper.NaoVazio(numeroContrato))
            {
                resultado.Erros.Add("Número do contrato não pode estar vazio");
            }
            else if (numeroContrato.Trim().Length < 3)
            {
                resultado.Avisos.Add($"Número do contrato muito curto: '{numeroContrato}'");
            }
        }

        private void ValidarCodigoArea(string codigoArea, ValidationResult resultado)
        {
            var validacao = ValidationHelper.ValidarCodigoArea(codigoArea);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Código de área inválido: {validacao.erro}");
            }
        }

        private void ValidarNumeroTelefone(string numeroTelefone, ValidationResult resultado)
        {
            var validacao = ValidationHelper.ValidarNumeroTelefone(numeroTelefone);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Número de telefone inválido: {validacao.erro}");
            }
        }

        private void ValidarTipoTelefone(string tipoTelefone, ValidationResult resultado)
        {
            if (!ValidationHelper.ValidarValoresPermitidos(tipoTelefone, "0", "1", "2", "4", "5"))
            {
                resultado.Erros.Add($"Tipo de telefone inválido: '{tipoTelefone}'. Valores permitidos: '0' (Outro), '1' (Casa), '2' (Trabalho), '4' (Familiar), '5' (Celular)");
            }
        }

        private void ValidarExtensao(string extensao, ValidationResult resultado)
        {
            // Extensão é opcional, mas se informada deve ser numérica
            if (!string.IsNullOrWhiteSpace(extensao) && extensao.Trim() != "0")
            {
                if (!ValidationHelper.IsNumerico(extensao.Trim()))
                {
                    resultado.Avisos.Add($"Extensão deve ser numérica: '{extensao}'");
                }
            }
        }

        private void ValidarDataCriacao(string dataCriacao, ValidationResult resultado)
        {
            var validacao = ValidationHelper.ValidarDataMMDDAAAA(dataCriacao);
            if (!validacao.valido)
            {
                resultado.Erros.Add($"Data de criação inválida: {validacao.erro}");
            }
        }

        private void ValidarStatusTelefone(string statusTelefone, ValidationResult resultado)
        {
            if (!ValidationHelper.ValidarValoresPermitidos(statusTelefone, "A", "I"))
            {
                resultado.Erros.Add($"Status inválido: '{statusTelefone}'. Valores permitidos: 'A' (Ativo), 'I' (Inativo)");
            }
        }

        private void ValidarRanking(string ranking, ValidationResult resultado)
        {
            if (!string.IsNullOrWhiteSpace(ranking))
            {
                var validacao = ValidationHelper.ValidarRanking(ranking);
                if (!validacao.valido)
                {
                    resultado.Erros.Add($"Ranking inválido: {validacao.erro}");
                }
            }
        }

        private void ValidarConsistenciaGeral(RegistroTelefone telefone, ValidationResult resultado)
        {
            // Validação: celulares devem ter 9 dígitos
            if (telefone.TipoTelefone == "5" && !string.IsNullOrEmpty(telefone.NumeroTelefone))
            {
                var numero = telefone.NumeroTelefone.Trim();
                if (numero.Length == 8)
                {
                    resultado.Avisos.Add("Celular com 8 dígitos pode estar desatualizado (esperado 9 dígitos)");
                }
            }

            // Validação: telefones inativos com ranking alto
            if (telefone.StatusTelefone == "I" && !string.IsNullOrEmpty(telefone.Ranking))
            {
                if (int.TryParse(telefone.Ranking.Trim(), out int rank) && rank <= 3)
                {
                    resultado.Avisos.Add($"Telefone inativo com ranking alto ({rank}) pode indicar inconsistência");
                }
            }

            // Validação: extensão em telefone celular
            if (telefone.TipoTelefone == "5" && telefone.TemExtensao)
            {
                resultado.Avisos.Add("Celular com extensão é incomum");
            }
        }

        #endregion
    }
}
