using System;
using System.ComponentModel.DataAnnotations;
using SistemaMFT.Core.Entities;
using SistemaMFT.Core.Common;

namespace SistemaMFT.Modules.Parcelas.Entities
{
    /// <summary>
    /// Entidade que representa um registro do arquivo PARCELAS_OUT.txt
    /// Layout: 228 caracteres por linha (pode ter 1653/1649 caracteres em alguns casos)
    /// Código de Registro: "000"
    /// Herda de BaseEntity para manter consistência com outros módulos
    /// </summary>
    public class RegistroParcela : BaseEntity
    {
        /// <summary>
        /// Tipo de registro - posições 1-3 (valor fixo "000")
        /// Identifica que este é um registro de parcela
        /// </summary>
        [Required]
        [StringLength(3)]
        public string TipoRegistro { get; set; } = "000";

        /// <summary>
        /// Grupo - posição 4 (1=QuintoAndar Locação, 2=QuintoCred Crédito)
        /// Define o tipo de negócio da parcela
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Grupo { get; set; } = string.Empty;

        /// <summary>
        /// Número do contrato - posições 5-29 (25 caracteres)
        /// Identificador do contrato ao qual a parcela pertence
        /// </summary>
        [Required]
        [StringLength(25)]
        public string NumeroContrato { get; set; } = string.Empty;

        /// <summary>
        /// Número da parcela - posições 30-32 (3 caracteres)
        /// Sequencial da parcela no contrato
        /// </summary>
        [Required]
        [StringLength(3)]
        public string NumeroParcela { get; set; } = string.Empty;

        /// <summary>
        /// Data de vencimento - posições 33-40 (8 caracteres - formato MMDDAAAA)
        /// Data em que a parcela vence
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataVencimento { get; set; } = string.Empty;

        /// <summary>
        /// Valor principal - posições 41-55 (15 caracteres)
        /// Valor principal da parcela (com 2 casas decimais implícitas)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorPrincipal { get; set; } = string.Empty;

        /// <summary>
        /// Valor de juros - posições 56-70 (15 caracteres)
        /// Valor de juros da parcela (com 2 casas decimais implícitas)
        /// </summary>
        [StringLength(15)]
        public string ValorJuros { get; set; } = string.Empty;

        /// <summary>
        /// Valor de multa - posições 71-85 (15 caracteres)
        /// Valor de multa da parcela (com 2 casas decimais implícitas)
        /// </summary>
        [StringLength(15)]
        public string ValorMulta { get; set; } = string.Empty;

        /// <summary>
        /// Status da parcela - posição 86 (1=Aberto, 2=Pago, 3=Cancelado)
        /// Situação atual da parcela
        /// </summary>
        [Required]
        [StringLength(1)]
        public string StatusParcela { get; set; } = string.Empty;

        /// <summary>
        /// Data de pagamento - posições 87-94 (8 caracteres - formato MMDDAAAA)
        /// Data em que a parcela foi paga (opcional)
        /// </summary>
        [StringLength(8)]
        public string DataPagamento { get; set; } = string.Empty;

        /// <summary>
        /// Valor pago - posições 95-109 (15 caracteres)
        /// Valor efetivamente pago (com 2 casas decimais implícitas)
        /// </summary>
        [StringLength(15)]
        public string ValorPago { get; set; } = string.Empty;

        /// <summary>
        /// Ranking - posições 225-228 (4 caracteres)
        /// Prioridade da parcela (1-9999)
        /// </summary>
        [StringLength(4)]
        public string Ranking { get; set; } = string.Empty;

        // Propriedades derivadas para facilitar o uso e exibição

        /// <summary>
        /// Descrição do grupo (QuintoAndar - Locação ou QuintoCred - Crédito)
        /// </summary>
        public string GrupoDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Descrição do status da parcela
        /// </summary>
        public string StatusDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Data de vencimento formatada para exibição (DD/MM/AAAA)
        /// </summary>
        public string DataVencimentoFormatada { get; set; } = string.Empty;

        /// <summary>
        /// Data de pagamento formatada para exibição (DD/MM/AAAA)
        /// </summary>
        public string DataPagamentoFormatada { get; set; } = string.Empty;

        /// <summary>
        /// Valor principal como decimal
        /// </summary>
        public decimal ValorPrincipalDecimal { get; set; }

        /// <summary>
        /// Valor de juros como decimal
        /// </summary>
        public decimal ValorJurosDecimal { get; set; }

        /// <summary>
        /// Valor de multa como decimal
        /// </summary>
        public decimal ValorMultaDecimal { get; set; }

        /// <summary>
        /// Valor pago como decimal
        /// </summary>
        public decimal ValorPagoDecimal { get; set; }

        /// <summary>
        /// Valor total da parcela (principal + juros + multa)
        /// </summary>
        public decimal ValorTotal => ValorPrincipalDecimal + ValorJurosDecimal + ValorMultaDecimal;

        /// <summary>
        /// Número da parcela como inteiro
        /// </summary>
        public int NumeroParcelaInt { get; set; }

        /// <summary>
        /// Ranking como número inteiro
        /// </summary>
        public int RankingNumerico { get; set; }

        /// <summary>
        /// Indica se a parcela está paga (status 2)
        /// </summary>
        public bool IsPaga => StatusParcela == "2";

        /// <summary>
        /// Indica se a parcela está cancelada (status 3)
        /// </summary>
        public bool IsCancelada => StatusParcela == "3";

        /// <summary>
        /// Indica se a parcela está em aberto (status 1)
        /// </summary>
        public bool IsAberta => StatusParcela == "1";

        /// <summary>
        /// Indica se a parcela tem data de pagamento
        /// </summary>
        public bool TemDataPagamento => !string.IsNullOrWhiteSpace(DataPagamento) && DataPagamento.Trim() != "00000000";

        /// <summary>
        /// Situação de atraso da parcela
        /// </summary>
        public string SituacaoAtraso { get; set; } = string.Empty;

        /// <summary>
        /// Dias de atraso (calculado)
        /// </summary>
        public int DiasAtraso { get; set; }

        /// <summary>
        /// Converte data de vencimento (MMDDAAAA) para DateTime
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Data de vencimento como DateTime ou null se inválida</returns>
        public DateTime? ObterDataVencimento()
        {
            var resultado = ValidationHelper.ValidarDataMMDDAAAA(DataVencimento);
            return resultado.valido ? resultado.dataFormatada : null;
        }

        /// <summary>
        /// Converte data de pagamento (MMDDAAAA) para DateTime
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Data de pagamento como DateTime ou null se inválida</returns>
        public DateTime? ObterDataPagamento()
        {
            if (!TemDataPagamento) return null;
            
            var resultado = ValidationHelper.ValidarDataMMDDAAAA(DataPagamento);
            return resultado.valido ? resultado.dataFormatada : null;
        }

        /// <summary>
        /// Calcula os dias de atraso baseado na data de vencimento
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Número de dias de atraso</returns>
        public int CalcularDiasAtraso()
        {
            var dataVenc = ObterDataVencimento();
            if (!dataVenc.HasValue) return 0;

            // Se está paga, não há atraso
            if (IsPaga) return 0;

            // Se está cancelada, não há atraso
            if (IsCancelada) return 0;

            // Calcula diferença entre hoje e vencimento
            var hoje = DateTime.Today;
            var diasAtraso = (hoje - dataVenc.Value).Days;

            return diasAtraso > 0 ? diasAtraso : 0;
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras da documentação
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return TipoRegistro == "000" &&
                   ValidationHelper.ValidarValoresPermitidos(Grupo, "1", "2") &&
                   ValidationHelper.NaoVazio(NumeroContrato) &&
                   ValidationHelper.NaoVazio(NumeroParcela) &&
                   ValidationHelper.NaoVazio(DataVencimento) &&
                   ValidationHelper.NaoVazio(ValorPrincipal) &&
                   ValidationHelper.ValidarValoresPermitidos(StatusParcela, "1", "2", "3");
        }

        /// <summary>
        /// Sobrescreve o método base para fornecer resumo específico da parcela
        /// </summary>
        /// <returns>String com resumo da parcela</returns>
        public override string ObterResumo()
        {
            return $"[PARCELA] {NumeroContrato} - Parcela {NumeroParcela} | Valor: {ValorTotal:C2} | Status: {StatusDescricao} | Vencimento: {DataVencimentoFormatada}";
        }

        /// <summary>
        /// Valida se a entidade atende aos requisitos específicos de parcela
        /// </summary>
        /// <returns>True se a entidade é válida</returns>
        public override bool ValidarBase()
        {
            return base.ValidarBase() && ValidarRegistro();
        }

        /// <summary>
        /// Atualiza as propriedades derivadas com base nos valores dos campos
        /// </summary>
        public void AtualizarPropriedadesDerivadas()
        {
            // Grupo
            GrupoDescricao = Grupo switch
            {
                "1" => "QuintoAndar - Locação",
                "2" => "QuintoCred - Crédito",
                _ => "Grupo Desconhecido"
            };

            // Status
            StatusDescricao = StatusParcela switch
            {
                "1" => "Aberto",
                "2" => "Pago",
                "3" => "Cancelado",
                _ => "Status Desconhecido"
            };

            // Datas formatadas
            var dataVencValidacao = ValidationHelper.ValidarDataMMDDAAAA(DataVencimento);
            DataVencimentoFormatada = dataVencValidacao.valido ? 
                dataVencValidacao.dataFormatada?.ToString("dd/MM/yyyy") ?? string.Empty : 
                DataVencimento;

            if (TemDataPagamento)
            {
                var dataPagValidacao = ValidationHelper.ValidarDataMMDDAAAA(DataPagamento);
                DataPagamentoFormatada = dataPagValidacao.valido ? 
                    dataPagValidacao.dataFormatada?.ToString("dd/MM/yyyy") ?? string.Empty : 
                    DataPagamento;
            }

            // Valores decimais
            var validacaoPrincipal = ValidationHelper.ValidarValorMonetario(ValorPrincipal, 2);
            ValorPrincipalDecimal = validacaoPrincipal.valido && validacaoPrincipal.valorFormatado.HasValue ? validacaoPrincipal.valorFormatado.Value : 0;

            var validacaoJuros = ValidationHelper.ValidarValorMonetario(ValorJuros, 2);
            ValorJurosDecimal = validacaoJuros.valido && validacaoJuros.valorFormatado.HasValue ? validacaoJuros.valorFormatado.Value : 0;

            var validacaoMulta = ValidationHelper.ValidarValorMonetario(ValorMulta, 2);
            ValorMultaDecimal = validacaoMulta.valido && validacaoMulta.valorFormatado.HasValue ? validacaoMulta.valorFormatado.Value : 0;

            var validacaoPago = ValidationHelper.ValidarValorMonetario(ValorPago, 2);
            ValorPagoDecimal = validacaoPago.valido && validacaoPago.valorFormatado.HasValue ? validacaoPago.valorFormatado.Value : 0;

            // Número da parcela
            if (int.TryParse(NumeroParcela?.Trim(), out int numParcela))
            {
                NumeroParcelaInt = numParcela;
            }

            // Ranking numérico
            if (int.TryParse(Ranking?.Trim(), out int rankingNum))
            {
                RankingNumerico = rankingNum;
            }

            // Calcular dias de atraso e situação
            DiasAtraso = CalcularDiasAtraso();
            
            // Situação de atraso - lógica corrigida conforme feedback do usuário
            if (IsPaga)
            {
                // Se está pago mas não tem data de pagamento, é inconsistente
                if (!TemDataPagamento)
                {
                    SituacaoAtraso = "PAGO SEM DATA";
                }
                else
                {
                    SituacaoAtraso = "PAGO";
                }
            }
            else if (IsCancelada)
            {
                SituacaoAtraso = "CANCELADO";
            }
            else if (DiasAtraso > 0)
            {
                SituacaoAtraso = $"DIAS_ATRASO: {DiasAtraso}";
            }
            else
            {
                SituacaoAtraso = "EM_DIA";
            }
        }
    }
}
