using System.Text;
using SistemaMFT.Configuracao;

namespace SistemaMFT.Utilitarios
{
    /// <summary>
    /// Classe utilitária para manipulação de arquivos com codificação ISO-8859-1
    /// Fornece métodos específicos para leitura, escrita e processamento de arquivos MFT
    /// </summary>
    public static class ManipuladorArquivos
    {
        /// <summary>
        /// Codificação padrão ISO-8859-1 para arquivos MFT
        /// </summary>
        public static readonly Encoding CodificacaoISO88591 = Encoding.GetEncoding("ISO-8859-1");

        /// <summary>
        /// Lê todas as linhas de um arquivo usando codificação ISO-8859-1
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Array de strings com todas as linhas</returns>
        /// <exception cref="FileNotFoundException">Quando o arquivo não existe</exception>
        public static string[] LerTodasLinhas(string caminhoArquivo)
        {
            if (!File.Exists(caminhoArquivo))
            {
                throw new FileNotFoundException($"Arquivo não encontrado: {caminhoArquivo}");
            }

            return File.ReadAllLines(caminhoArquivo, CodificacaoISO88591);
        }

        /// <summary>
        /// Escreve todas as linhas em um arquivo usando codificação ISO-8859-1
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <param name="linhas">Array de strings com as linhas</param>
        public static void EscreverTodasLinhas(string caminhoArquivo, string[] linhas)
        {
            var diretorio = Path.GetDirectoryName(caminhoArquivo);
            if (!string.IsNullOrEmpty(diretorio) && !Directory.Exists(diretorio))
            {
                Directory.CreateDirectory(diretorio);
            }

            File.WriteAllLines(caminhoArquivo, linhas, CodificacaoISO88591);
        }

        /// <summary>
        /// Lê todo o conteúdo de um arquivo usando codificação ISO-8859-1
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Conteúdo completo do arquivo</returns>
        public static string LerTodoTexto(string caminhoArquivo)
        {
            if (!File.Exists(caminhoArquivo))
            {
                throw new FileNotFoundException($"Arquivo não encontrado: {caminhoArquivo}");
            }

            return File.ReadAllText(caminhoArquivo, CodificacaoISO88591);
        }

        /// <summary>
        /// Escreve todo o texto em um arquivo usando codificação ISO-8859-1
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <param name="conteudo">Conteúdo para escrever</param>
        public static void EscreverTodoTexto(string caminhoArquivo, string conteudo)
        {
            var diretorio = Path.GetDirectoryName(caminhoArquivo);
            if (!string.IsNullOrEmpty(diretorio) && !Directory.Exists(diretorio))
            {
                Directory.CreateDirectory(diretorio);
            }

            File.WriteAllText(caminhoArquivo, conteudo, CodificacaoISO88591);
        }

        /// <summary>
        /// Cria um backup de um arquivo movendo-o para o diretório de backup
        /// </summary>
        /// <param name="caminhoArquivoOriginal">Caminho do arquivo original</param>
        /// <param name="diretorioBackup">Diretório de backup</param>
        /// <param name="prefixoBackup">Prefixo para o nome do backup</param>
        /// <returns>Caminho do arquivo de backup criado</returns>
        public static string CriarBackup(string caminhoArquivoOriginal, string diretorioBackup, string prefixoBackup = "backup_")
        {
            if (!File.Exists(caminhoArquivoOriginal))
            {
                throw new FileNotFoundException($"Arquivo não encontrado: {caminhoArquivoOriginal}");
            }

            if (!Directory.Exists(diretorioBackup))
            {
                Directory.CreateDirectory(diretorioBackup);
            }

            var nomeArquivo = Path.GetFileName(caminhoArquivoOriginal);
            var timestampBackup = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var nomeBackup = $"{prefixoBackup}{timestampBackup}_{nomeArquivo}";
            var caminhoBackup = Path.Combine(diretorioBackup, nomeBackup);

            File.Copy(caminhoArquivoOriginal, caminhoBackup, true);

            return caminhoBackup;
        }

        /// <summary>
        /// Move um arquivo para um diretório específico
        /// </summary>
        /// <param name="caminhoArquivoOriginal">Caminho do arquivo original</param>
        /// <param name="diretorioDestino">Diretório de destino</param>
        /// <param name="substituir">Se deve substituir arquivo existente</param>
        /// <returns>Caminho do arquivo movido</returns>
        public static string MoverArquivo(string caminhoArquivoOriginal, string diretorioDestino, bool substituir = true)
        {
            if (!File.Exists(caminhoArquivoOriginal))
            {
                throw new FileNotFoundException($"Arquivo não encontrado: {caminhoArquivoOriginal}");
            }

            if (!Directory.Exists(diretorioDestino))
            {
                Directory.CreateDirectory(diretorioDestino);
            }

            var nomeArquivo = Path.GetFileName(caminhoArquivoOriginal);
            var caminhoDestino = Path.Combine(diretorioDestino, nomeArquivo);

            if (File.Exists(caminhoDestino))
            {
                if (substituir)
                {
                    File.Delete(caminhoDestino);
                }
                else
                {
                    throw new InvalidOperationException($"Arquivo já existe no destino: {caminhoDestino}");
                }
            }

            File.Move(caminhoArquivoOriginal, caminhoDestino);

            return caminhoDestino;
        }

        /// <summary>
        /// Copia um arquivo para um diretório específico
        /// </summary>
        /// <param name="caminhoArquivoOriginal">Caminho do arquivo original</param>
        /// <param name="diretorioDestino">Diretório de destino</param>
        /// <param name="substituir">Se deve substituir arquivo existente</param>
        /// <returns>Caminho do arquivo copiado</returns>
        public static string CopiarArquivo(string caminhoArquivoOriginal, string diretorioDestino, bool substituir = true)
        {
            if (!File.Exists(caminhoArquivoOriginal))
            {
                throw new FileNotFoundException($"Arquivo não encontrado: {caminhoArquivoOriginal}");
            }

            if (!Directory.Exists(diretorioDestino))
            {
                Directory.CreateDirectory(diretorioDestino);
            }

            var nomeArquivo = Path.GetFileName(caminhoArquivoOriginal);
            var caminhoDestino = Path.Combine(diretorioDestino, nomeArquivo);

            File.Copy(caminhoArquivoOriginal, caminhoDestino, substituir);

            return caminhoDestino;
        }

        /// <summary>
        /// Obtém uma lista de arquivos em um diretório com padrão específico
        /// </summary>
        /// <param name="diretorio">Diretório para pesquisar</param>
        /// <param name="padraoArquivo">Padrão do arquivo (ex: "*.txt")</param>
        /// <returns>Array com caminhos dos arquivos encontrados</returns>
        public static string[] ObterArquivos(string diretorio, string padraoArquivo = "*.*")
        {
            if (!Directory.Exists(diretorio))
            {
                return Array.Empty<string>();
            }

            return Directory.GetFiles(diretorio, padraoArquivo);
        }

        /// <summary>
        /// Obtém informações sobre um arquivo
        /// </summary>
        /// <param name="caminhoArquivo">Caminho do arquivo</param>
        /// <returns>Informações do arquivo</returns>
        public static FileInfo ObterInformacoesArquivo(string caminhoArquivo)
        {
            if (!File.Exists(caminhoArquivo))
            {
                throw new FileNotFoundException($"Arquivo não encontrado: {caminhoArquivo}");
            }

            return new FileInfo(caminhoArquivo);
        }

        /// <summary>
        /// Verifica se um arquivo está em uso por outro processo
        /// </summary>
        /// <param name="caminhoArquivo">Caminho do arquivo</param>
        /// <returns>True se está em uso, False caso contrário</returns>
        public static bool ArquivoEstaEmUso(string caminhoArquivo)
        {
            if (!File.Exists(caminhoArquivo))
                return false;

            try
            {
                using var stream = new FileStream(caminhoArquivo, FileMode.Open, FileAccess.Read, FileShare.None);
                return false;
            }
            catch (IOException)
            {
                return true;
            }
        }

        /// <summary>
        /// Cria um nome de arquivo único adicionando timestamp
        /// </summary>
        /// <param name="caminhoArquivo">Caminho base do arquivo</param>
        /// <returns>Caminho único do arquivo</returns>
        public static string CriarNomeArquivoUnico(string caminhoArquivo)
        {
            var diretorio = Path.GetDirectoryName(caminhoArquivo) ?? string.Empty;
            var nomeArquivo = Path.GetFileNameWithoutExtension(caminhoArquivo);
            var extensao = Path.GetExtension(caminhoArquivo);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            var nomeUnico = $"{nomeArquivo}_{timestamp}{extensao}";
            return Path.Combine(diretorio, nomeUnico);
        }

        /// <summary>
        /// Valida se um arquivo tem a estrutura básica de um arquivo MFT
        /// </summary>
        /// <param name="caminhoArquivo">Caminho do arquivo</param>
        /// <returns>True se tem estrutura válida, False caso contrário</returns>
        public static bool ValidarEstruturaBasicaMFT(string caminhoArquivo)
        {
            try
            {
                var linhas = LerTodasLinhas(caminhoArquivo);

                if (linhas.Length < 2)
                    return false;

                // Primeira linha deve ser header (começar com "H")
                if (!linhas[0].StartsWith("H"))
                    return false;

                // Última linha deve ser trailer (começar com "T")
                if (!linhas[^1].StartsWith("T"))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Limpa arquivos antigos de um diretório
        /// </summary>
        /// <param name="diretorio">Diretório para limpar</param>
        /// <param name="diasParaManterArquivos">Número de dias para manter arquivos</param>
        /// <param name="padraoArquivo">Padrão dos arquivos para limpar</param>
        /// <returns>Número de arquivos removidos</returns>
        public static int LimparArquivosAntigos(string diretorio, int diasParaManterArquivos, string padraoArquivo = "*.*")
        {
            if (!Directory.Exists(diretorio))
                return 0;

            var dataLimite = DateTime.Now.AddDays(-diasParaManterArquivos);
            var arquivos = Directory.GetFiles(diretorio, padraoArquivo);
            var arquivosRemovidos = 0;

            foreach (var arquivo in arquivos)
            {
                var info = new FileInfo(arquivo);
                if (info.CreationTime < dataLimite)
                {
                    try
                    {
                        File.Delete(arquivo);
                        arquivosRemovidos++;
                    }
                    catch
                    {
                        // Ignora erros ao deletar arquivos
                    }
                }
            }

            return arquivosRemovidos;
        }

        /// <summary>
        /// Gera um nome de arquivo baseado na configuração e data atual
        /// </summary>
        /// <param name="tipoArquivo">Tipo do arquivo (ex: "CONTRATOS_OUT")</param>
        /// <param name="configuracao">Configuração do sistema</param>
        /// <returns>Nome do arquivo gerado</returns>
        public static string GerarNomeArquivo(string tipoArquivo, ConfiguracaoArquivos configuracao)
        {
            var dataAtual = DateTime.Now.ToString(configuracao.FormatoDataArquivo);
            return $"{tipoArquivo}_{dataAtual}.txt";
        }
    }
} 