using System;
using System.ComponentModel.DataAnnotations;
using SistemaMFT.Core.Entities;
using SistemaMFT.Core.Common;

namespace SistemaMFT.Modules.Contratos.Entities
{
    /// <summary>
    /// Entidade que representa um registro do arquivo CONTRATOS_OUT.txt
    /// Layout: 155 caracteres por linha
    /// Código de Registro: "100"
    /// Herda de BaseEntity para manter consistência com outros módulos
    /// </summary>
    public class RegistroContrato : BaseEntity
    {
        /// <summary>
        /// Tipo de registro - posições 1-3 (valor fixo "100")
        /// Identifica que este é um registro de contrato
        /// </summary>
        [Required]
        [StringLength(3)]
        public string TipoRegistro { get; set; } = "100";

        /// <summary>
        /// Grupo - posição 4 (1=QuintoAndar Locação, 2=QuintoCred Crédito)
        /// Define o tipo de negócio do contrato
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Grupo { get; set; } = string.Empty;

        /// <summary>
        /// Número do contrato - posições 5-29 (25 caracteres)
        /// Identificador único do contrato no sistema
        /// </summary>
        [Required]
        [StringLength(25)]
        public string NumeroContrato { get; set; } = string.Empty;

        /// <summary>
        /// CPF/CNPJ do cliente - posições 30-43 (14 caracteres)
        /// Documento de identificação do cliente (11 dígitos para CPF, 14 para CNPJ)
        /// </summary>
        [Required]
        [StringLength(14)]
        public string CpfCnpj { get; set; } = string.Empty;

        /// <summary>
        /// Nome do cliente - posições 44-123 (80 caracteres)
        /// Nome completo ou razão social do cliente
        /// </summary>
        [Required]
        [StringLength(80)]
        public string NomeCliente { get; set; } = string.Empty;

        /// <summary>
        /// Status do contrato - posição 124 (A=Ativo, I=Inativo, C=Cancelado)
        /// Situação atual do contrato
        /// </summary>
        [Required]
        [StringLength(1)]
        public string StatusContrato { get; set; } = string.Empty;

        /// <summary>
        /// Data de início - posições 125-132 (8 caracteres - formato MMDDAAAA)
        /// Data de início da vigência do contrato
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataInicio { get; set; } = string.Empty;

        /// <summary>
        /// Data de fim - posições 133-140 (8 caracteres - formato MMDDAAAA)
        /// Data de fim da vigência do contrato
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataFim { get; set; } = string.Empty;

        /// <summary>
        /// Valor do contrato - posições 141-155 (15 caracteres)
        /// Valor total do contrato em centavos (dividir por 100)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorContrato { get; set; } = string.Empty;

        // Propriedades derivadas para facilitar o uso e exibição
        
        /// <summary>
        /// Tipo do documento (CPF ou CNPJ) - calculado automaticamente
        /// </summary>
        public string TipoDocumento { get; set; } = string.Empty;

        /// <summary>
        /// Documento formatado para exibição (XXX.XXX.XXX-XX ou XX.XXX.XXX/XXXX-XX)
        /// </summary>
        public string DocumentoFormatado { get; set; } = string.Empty;

        /// <summary>
        /// Descrição do grupo (QuintoAndar - Locação ou QuintoCred - Crédito)
        /// </summary>
        public string GrupoDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Descrição do status (Ativo, Inativo, Cancelado)
        /// </summary>
        public string StatusDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Data de início formatada para exibição (DD/MM/AAAA)
        /// </summary>
        public string DataInicioFormatada { get; set; } = string.Empty;

        /// <summary>
        /// Data de fim formatada para exibição (DD/MM/AAAA)
        /// </summary>
        public string DataFimFormatada { get; set; } = string.Empty;

        /// <summary>
        /// Valor do contrato como decimal (já convertido de centavos)
        /// </summary>
        public decimal ValorContratoDecimal { get; set; }

        /// <summary>
        /// Valor do contrato formatado para exibição (R$ X.XXX,XX)
        /// </summary>
        public string ValorContratoFormatado { get; set; } = string.Empty;

        /// <summary>
        /// Converte valor do contrato para decimal (divide por 100 para obter centavos)
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Valor do contrato como decimal</returns>
        public decimal ObterValorContrato()
        {
            if (string.IsNullOrEmpty(ValorContrato))
                return 0;

            if (long.TryParse(ValorContrato.Trim(), out long valor))
                return valor / 100.00m;
            
            return 0;
        }

        /// <summary>
        /// Converte data de início (MMDDAAAA) para DateTime
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Data de início como DateTime ou null se inválida</returns>
        public DateTime? ObterDataInicio()
        {
            var resultado = ValidationHelper.ValidarDataMMDDAAAA(DataInicio);
            return resultado.valido ? resultado.dataFormatada : null;
        }

        /// <summary>
        /// Converte data de fim (MMDDAAAA) para DateTime
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Data de fim como DateTime ou null se inválida</returns>
        public DateTime? ObterDataFim()
        {
            var resultado = ValidationHelper.ValidarDataMMDDAAAA(DataFim);
            return resultado.valido ? resultado.dataFormatada : null;
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras da documentação
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return TipoRegistro == "100" &&
                   ValidationHelper.ValidarValoresPermitidos(Grupo, "1", "2") &&
                   ValidationHelper.NaoVazio(NumeroContrato) &&
                   ValidationHelper.NaoVazio(CpfCnpj) &&
                   ValidationHelper.NaoVazio(NomeCliente) &&
                   ValidationHelper.ValidarValoresPermitidos(StatusContrato, "A", "I", "C");
        }

        /// <summary>
        /// Sobrescreve o método base para fornecer resumo específico do contrato
        /// </summary>
        /// <returns>String com resumo do contrato</returns>
        public override string ObterResumo()
        {
            return $"[CONTRATO] {NumeroContrato} | Cliente: {StringHelper.TruncarTexto(NomeCliente, 30)} | Status: {StatusDescricao} | Valor: {ValorContratoFormatado}";
        }

        /// <summary>
        /// Valida se a entidade atende aos requisitos específicos de contrato
        /// </summary>
        /// <returns>True se a entidade é válida</returns>
        public override bool ValidarBase()
        {
            return base.ValidarBase() && ValidarRegistro();
        }
    }
}
