using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Configuracao
{
    /// <summary>
    /// Configuração principal para APIs externas
    /// </summary>
    public class ConfiguracaoAPI
    {
        /// <summary>
        /// Configurações da API CyberAgreements
        /// </summary>
        [Required]
        public ConfiguracaoCyberAgreements CyberAgreements { get; set; } = new ConfiguracaoCyberAgreements();
    }

    /// <summary>
    /// Configurações específicas da API CyberAgreements
    /// </summary>
    public class ConfiguracaoCyberAgreements
    {
        /// <summary>
        /// Host da API CyberAgreements
        /// </summary>
        [Required]
        public string Host { get; set; } = string.Empty;

        /// <summary>
        /// Token de autorização (Basic Auth)
        /// </summary>
        [Required]
        public string Authorization { get; set; } = string.Empty;

        /// <summary>
        /// Tipo de conteúdo das requisições
        /// </summary>
        [Required]
        public string ContentType { get; set; } = "application/json";

        /// <summary>
        /// Timeout em minutos para as requisições
        /// </summary>
        public int TimeoutMinutos { get; set; } = 15;

        /// <summary>
        /// Sequência obrigatória de chamadas da API
        /// </summary>
        public List<string> SequenciaObrigatoria { get; set; } = new List<string>();

        /// <summary>
        /// Obtém a URL base da API
        /// </summary>
        /// <returns>URL base</returns>
        public string ObterUrlBase()
        {
            return $"https://{Host}";
        }

        /// <summary>
        /// Obtém os headers HTTP padrão para requisições
        /// </summary>
        /// <returns>Dictionary com os headers</returns>
        public Dictionary<string, string> ObterHeadersPadrao()
        {
            return new Dictionary<string, string>
            {
                { "Authorization", Authorization },
                { "Content-Type", ContentType },
                { "Accept", ContentType }
            };
        }

        /// <summary>
        /// Obtém o timeout em milissegundos
        /// </summary>
        /// <returns>Timeout em milissegundos</returns>
        public int ObterTimeoutMilissegundos()
        {
            return TimeoutMinutos * 60 * 1000;
        }

        /// <summary>
        /// Valida se a configuração está completa
        /// </summary>
        /// <returns>True se válida, False caso contrário</returns>
        public bool ValidarConfiguracao()
        {
            return !string.IsNullOrEmpty(Host) &&
                   !string.IsNullOrEmpty(Authorization) &&
                   !string.IsNullOrEmpty(ContentType) &&
                   SequenciaObrigatoria.Count > 0;
        }

        /// <summary>
        /// Verifica se a sequência de chamadas está correta
        /// </summary>
        /// <param name="chamadasRealizadas">Lista de chamadas já realizadas</param>
        /// <returns>True se a sequência está correta, False caso contrário</returns>
        public bool ValidarSequenciaChamadas(List<string> chamadasRealizadas)
        {
            if (chamadasRealizadas.Count > SequenciaObrigatoria.Count)
                return false;

            for (int i = 0; i < chamadasRealizadas.Count; i++)
            {
                if (chamadasRealizadas[i] != SequenciaObrigatoria[i])
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Obtém a próxima chamada da sequência
        /// </summary>
        /// <param name="chamadasRealizadas">Lista de chamadas já realizadas</param>
        /// <returns>Nome da próxima chamada ou null se a sequência estiver completa</returns>
        public string? ObterProximaChamada(List<string> chamadasRealizadas)
        {
            if (chamadasRealizadas.Count >= SequenciaObrigatoria.Count)
                return null;

            if (!ValidarSequenciaChamadas(chamadasRealizadas))
                return null;

            return SequenciaObrigatoria[chamadasRealizadas.Count];
        }

        /// <summary>
        /// Verifica se a sequência está completa
        /// </summary>
        /// <param name="chamadasRealizadas">Lista de chamadas já realizadas</param>
        /// <returns>True se completa, False caso contrário</returns>
        public bool SequenciaCompleta(List<string> chamadasRealizadas)
        {
            return chamadasRealizadas.Count == SequenciaObrigatoria.Count &&
                   ValidarSequenciaChamadas(chamadasRealizadas);
        }
    }

    /// <summary>
    /// Modelo para resposta da API CyberAgreements
    /// </summary>
    public class RespostaCyberAgreements
    {
        /// <summary>
        /// Indica se a chamada foi bem-sucedida
        /// </summary>
        public bool Sucesso { get; set; }

        /// <summary>
        /// Mensagem de retorno
        /// </summary>
        public string Mensagem { get; set; } = string.Empty;

        /// <summary>
        /// Código de status HTTP
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// Dados de resposta (JSON)
        /// </summary>
        public string Dados { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp da chamada
        /// </summary>
        public DateTime TimestampChamada { get; set; } = DateTime.Now;

        /// <summary>
        /// Nome do endpoint chamado
        /// </summary>
        public string EndpointChamado { get; set; } = string.Empty;

        /// <summary>
        /// Tempo de resposta em milissegundos
        /// </summary>
        public long TempoRespostaMilissegundos { get; set; }
    }

    /// <summary>
    /// Modelo para requisição da API CyberAgreements
    /// </summary>
    public class RequisicaoCyberAgreements
    {
        /// <summary>
        /// Endpoint da API
        /// </summary>
        [Required]
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// Método HTTP (GET, POST, etc.)
        /// </summary>
        [Required]
        public string MetodoHttp { get; set; } = "POST";

        /// <summary>
        /// Dados da requisição (JSON)
        /// </summary>
        public string DadosRequisicao { get; set; } = string.Empty;

        /// <summary>
        /// Headers adicionais para a requisição
        /// </summary>
        public Dictionary<string, string> HeadersAdicionais { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Timeout específico para esta requisição (em milissegundos)
        /// </summary>
        public int? TimeoutEspecifico { get; set; }

        /// <summary>
        /// Número de tentativas em caso de erro
        /// </summary>
        public int NumeroTentativas { get; set; } = 1;

        /// <summary>
        /// Intervalo entre tentativas (em segundos)
        /// </summary>
        public int IntervaloTentativasSegundos { get; set; } = 5;
    }
} 