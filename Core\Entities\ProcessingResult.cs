using System;
using System.Collections.Generic;
using System.Linq;

namespace SistemaMFT.Core.Entities
{
    /// <summary>
    /// Resultado do processamento de uma linha individual
    /// Usado por todos os processadores para retornar resultados padronizados
    /// Contém a entidade processada e informações sobre erros/avisos
    /// </summary>
    /// <typeparam name="TEntity">Tipo da entidade processada</typeparam>
    public class ProcessingResult<TEntity> where TEntity : class
    {
        /// <summary>
        /// Indica se o processamento foi bem-sucedido
        /// True quando a entidade foi criada sem erros críticos
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Entidade processada (pode ser null se houve erro crítico)
        /// Contém os dados extraídos e validados da linha
        /// </summary>
        public TEntity? Registro { get; set; }

        /// <summary>
        /// Lista de erros encontrados durante o processamento
        /// Erros impedem que a entidade seja considerada válida
        /// </summary>
        public List<string> Erros { get; set; } = new List<string>();

        /// <summary>
        /// Lista de avisos sobre o processamento
        /// Avisos não impedem a validação mas indicam problemas menores
        /// </summary>
        public List<string> Avisos { get; set; } = new List<string>();

        /// <summary>
        /// Número da linha processada
        /// Usado para rastreamento e logs
        /// </summary>
        public int NumeroLinha { get; set; }

        /// <summary>
        /// Linha original que foi processada
        /// Mantida para auditoria
        /// </summary>
        public string LinhaOriginal { get; set; } = string.Empty;

        /// <summary>
        /// Cria um resultado de sucesso com a entidade processada
        /// </summary>
        /// <param name="entidade">Entidade processada com sucesso</param>
        /// <param name="numeroLinha">Número da linha processada</param>
        /// <param name="linhaOriginal">Linha original do arquivo</param>
        /// <returns>Resultado de processamento bem-sucedido</returns>
        public static ProcessingResult<TEntity> Sucesso(TEntity entidade, int numeroLinha, string linhaOriginal)
        {
            return new ProcessingResult<TEntity>
            {
                IsValid = true,
                Registro = entidade,
                NumeroLinha = numeroLinha,
                LinhaOriginal = linhaOriginal
            };
        }

        /// <summary>
        /// Cria um resultado de erro com lista de problemas encontrados
        /// </summary>
        /// <param name="erros">Lista de erros encontrados</param>
        /// <param name="numeroLinha">Número da linha com erro</param>
        /// <param name="linhaOriginal">Linha original do arquivo</param>
        /// <returns>Resultado de processamento com erro</returns>
        public static ProcessingResult<TEntity> Erro(IEnumerable<string> erros, int numeroLinha, string linhaOriginal)
        {
            return new ProcessingResult<TEntity>
            {
                IsValid = false,
                Registro = null,
                Erros = erros.ToList(),
                NumeroLinha = numeroLinha,
                LinhaOriginal = linhaOriginal
            };
        }

        /// <summary>
        /// Adiciona um erro ao resultado
        /// </summary>
        /// <param name="erro">Mensagem de erro</param>
        public void AdicionarErro(string erro)
        {
            Erros.Add(erro);
            IsValid = false;
        }

        /// <summary>
        /// Adiciona um aviso ao resultado
        /// </summary>
        /// <param name="aviso">Mensagem de aviso</param>
        public void AdicionarAviso(string aviso)
        {
            Avisos.Add(aviso);
        }
    }

    /// <summary>
    /// Resultado do processamento de um arquivo completo
    /// Agrega resultados de todas as linhas processadas
    /// </summary>
    /// <typeparam name="TEntity">Tipo das entidades processadas</typeparam>
    public class FileProcessingResult<TEntity> where TEntity : class
    {
        /// <summary>
        /// Nome do arquivo processado
        /// </summary>
        public string NomeArquivo { get; set; } = string.Empty;

        /// <summary>
        /// Caminho completo do arquivo
        /// </summary>
        public string CaminhoArquivo { get; set; } = string.Empty;

        /// <summary>
        /// Data e hora do processamento
        /// </summary>
        public DateTime DataProcessamento { get; set; } = DateTime.Now;

        /// <summary>
        /// Lista de entidades processadas com sucesso
        /// </summary>
        public List<TEntity> RegistrosValidos { get; set; } = new List<TEntity>();

        /// <summary>
        /// Lista de resultados com erro
        /// </summary>
        public List<ProcessingResult<TEntity>> RegistrosComErro { get; set; } = new List<ProcessingResult<TEntity>>();

        /// <summary>
        /// Indica se o arquivo foi processado completamente
        /// </summary>
        public bool ProcessadoComSucesso { get; set; }

        /// <summary>
        /// Total de linhas processadas
        /// </summary>
        public int TotalLinhas => RegistrosValidos.Count + RegistrosComErro.Count;

        /// <summary>
        /// Total de registros válidos
        /// </summary>
        public int TotalValidos => RegistrosValidos.Count;

        /// <summary>
        /// Total de registros com erro
        /// </summary>
        public int TotalErros => RegistrosComErro.Count;

        /// <summary>
        /// Percentual de sucesso do processamento
        /// </summary>
        public double PercentualSucesso => TotalLinhas > 0 ? (double)TotalValidos / TotalLinhas * 100 : 0;
    }
}
