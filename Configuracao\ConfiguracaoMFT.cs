using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Configuracao
{
    /// <summary>
    /// Configuração principal do sistema MFT
    /// Mapeia diretamente para o arquivo appsettings.json
    /// </summary>
    public class ConfiguracaoMFT
    {
        /// <summary>
        /// Configurações do servidor MFT
        /// </summary>
        [Required]
        public ConfiguracaoServidorMFT ServidorMFT { get; set; } = new ConfiguracaoServidorMFT();

        /// <summary>
        /// Configurações do servidor SFTP
        /// </summary>
        [Required]
        public ConfiguracaoServidorSFTP ServidorSFTP { get; set; } = new ConfiguracaoServidorSFTP();

        /// <summary>
        /// Configurações de criptografia GPG
        /// </summary>
        [Required]
        public ConfiguracaoGPG ConfiguracaoGPG { get; set; } = new ConfiguracaoGPG();

        /// <summary>
        /// Configurações de manipulação de arquivos
        /// </summary>
        [Required]
        public ConfiguracaoArquivos ConfiguracaoArquivos { get; set; } = new ConfiguracaoArquivos();

        /// <summary>
        /// Configurações de frequência de processamento
        /// </summary>
        [Required]
        public ConfiguracaoFrequencias ConfiguracaoFrequencias { get; set; } = new ConfiguracaoFrequencias();
    }

    /// <summary>
    /// Configurações do servidor MFT
    /// </summary>
    public class ConfiguracaoServidorMFT
    {
        /// <summary>
        /// Host do servidor MFT
        /// </summary>
        [Required]
        public string Host { get; set; } = string.Empty;

        /// <summary>
        /// Porta do servidor MFT
        /// </summary>
        [Required]
        public int Porta { get; set; } = 8448;

        /// <summary>
        /// Indica se deve usar HTTPS
        /// </summary>
        public bool UsarHTTPS { get; set; } = true;

        /// <summary>
        /// Timeout em segundos para conexões
        /// </summary>
        public int TimeoutSegundos { get; set; } = 60;

        /// <summary>
        /// Obtém a URL completa do servidor MFT
        /// </summary>
        /// <returns>URL completa</returns>
        public string ObterUrlCompleta()
        {
            var protocolo = UsarHTTPS ? "https" : "http";
            return $"{protocolo}://{Host}:{Porta}";
        }
    }

    /// <summary>
    /// Configurações do servidor SFTP
    /// </summary>
    public class ConfiguracaoServidorSFTP
    {
        /// <summary>
        /// Host do servidor SFTP
        /// </summary>
        [Required]
        public string Host { get; set; } = string.Empty;

        /// <summary>
        /// Porta do servidor SFTP
        /// </summary>
        [Required]
        public int Porta { get; set; } = 22;

        /// <summary>
        /// Usuário para autenticação
        /// </summary>
        [Required]
        public string Usuario { get; set; } = string.Empty;

        /// <summary>
        /// Senha para autenticação
        /// </summary>
        [Required]
        public string Senha { get; set; } = string.Empty;

        /// <summary>
        /// Diretório remoto padrão
        /// </summary>
        [Required]
        public string DiretorioRemoto { get; set; } = string.Empty;

        /// <summary>
        /// Timeout em segundos para conexões SFTP
        /// </summary>
        public int TimeoutSegundos { get; set; } = 30;

        /// <summary>
        /// Valida se todas as configurações obrigatórias estão preenchidas
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarConfiguracao()
        {
            return !string.IsNullOrEmpty(Host) &&
                   !string.IsNullOrEmpty(Usuario) &&
                   !string.IsNullOrEmpty(Senha) &&
                   !string.IsNullOrEmpty(DiretorioRemoto) &&
                   Porta > 0;
        }
    }

    /// <summary>
    /// Configurações de criptografia GPG
    /// </summary>
    public class ConfiguracaoGPG
    {
        /// <summary>
        /// Email do destinatário para criptografia
        /// </summary>
        [Required]
        public string EmailDestinatario { get; set; } = string.Empty;

        /// <summary>
        /// Caminho para o arquivo da chave pública
        /// </summary>
        [Required]
        public string CaminhoChavePublica { get; set; } = string.Empty;

        /// <summary>
        /// Caminho para o arquivo da chave privada
        /// </summary>
        [Required]
        public string CaminhoChavePrivada { get; set; } = string.Empty;

        /// <summary>
        /// Senha da chave privada (opcional)
        /// </summary>
        public string SenhaChavePrivada { get; set; } = string.Empty;

        /// <summary>
        /// Verifica se os arquivos de chave existem
        /// </summary>
        /// <returns>True se existem, False caso contrário</returns>
        public bool ValidarChaves()
        {
            return File.Exists(CaminhoChavePublica) && File.Exists(CaminhoChavePrivada);
        }
    }

    /// <summary>
    /// Configurações de manipulação de arquivos
    /// </summary>
    public class ConfiguracaoArquivos
    {
        /// <summary>
        /// Codificação padrão dos arquivos
        /// </summary>
        [Required]
        public string CodificacaoArquivos { get; set; } = "ISO-8859-1";

        /// <summary>
        /// Diretório para arquivos de entrada
        /// </summary>
        [Required]
        public string DiretorioEntrada { get; set; } = string.Empty;

        /// <summary>
        /// Diretório para arquivos de saída
        /// </summary>
        [Required]
        public string DiretorioSaida { get; set; } = string.Empty;

        /// <summary>
        /// Diretório para arquivos processados
        /// </summary>
        [Required]
        public string DiretorioProcessados { get; set; } = string.Empty;

        /// <summary>
        /// Diretório para arquivos com erro
        /// </summary>
        [Required]
        public string DiretorioErros { get; set; } = string.Empty;

        /// <summary>
        /// Prefixo para arquivos de backup
        /// </summary>
        public string PrefixoBackup { get; set; } = "backup_";

        /// <summary>
        /// Formato de data para nomes de arquivos
        /// </summary>
        public string FormatoDataArquivo { get; set; } = "ddMMyyyy";

        /// <summary>
        /// Cria os diretórios necessários se não existirem
        /// </summary>
        public void CriarDiretorios()
        {
            var diretorios = new[]
            {
                DiretorioEntrada,
                DiretorioSaida,
                DiretorioProcessados,
                DiretorioErros
            };

            foreach (var diretorio in diretorios)
            {
                if (!string.IsNullOrEmpty(diretorio) && !Directory.Exists(diretorio))
                {
                    Directory.CreateDirectory(diretorio);
                }
            }
        }
    }

    /// <summary>
    /// Configurações de frequência de processamento
    /// </summary>
    public class ConfiguracaoFrequencias
    {
        /// <summary>
        /// Lista de arquivos processados diariamente
        /// </summary>
        public List<string> ArquivosDiarios { get; set; } = new List<string>();

        /// <summary>
        /// Lista de arquivos processados semanalmente
        /// </summary>
        public List<string> ArquivosSemanais { get; set; } = new List<string>();

        /// <summary>
        /// Lista de arquivos processados de hora em hora
        /// </summary>
        public List<string> ArquivosHorarios { get; set; } = new List<string>();

        /// <summary>
        /// Verifica se um arquivo é processado diariamente
        /// </summary>
        /// <param name="nomeArquivo">Nome do arquivo</param>
        /// <returns>True se é diário, False caso contrário</returns>
        public bool EhArquivoDiario(string nomeArquivo)
        {
            return ArquivosDiarios.Contains(nomeArquivo, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Verifica se um arquivo é processado semanalmente
        /// </summary>
        /// <param name="nomeArquivo">Nome do arquivo</param>
        /// <returns>True se é semanal, False caso contrário</returns>
        public bool EhArquivoSemanal(string nomeArquivo)
        {
            return ArquivosSemanais.Contains(nomeArquivo, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Verifica se um arquivo é processado de hora em hora
        /// </summary>
        /// <param name="nomeArquivo">Nome do arquivo</param>
        /// <returns>True se é horário, False caso contrário</returns>
        public bool EhArquivoHorario(string nomeArquivo)
        {
            return ArquivosHorarios.Contains(nomeArquivo, StringComparer.OrdinalIgnoreCase);
        }
    }
} 