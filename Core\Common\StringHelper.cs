using System;
using System.Globalization;
using System.Text;

namespace SistemaMFT.Core.Common
{
    /// <summary>
    /// Utilitários para manipulação de strings
    /// Fornece operações comuns de string usadas pelos módulos
    /// Centraliza lógica de formatação e conversão
    /// </summary>
    public static class StringHelper
    {
        /// <summary>
        /// Extrai substring de forma segura, tratando índices inválidos
        /// Usado para extrair campos de posições fixas nos arquivos
        /// </summary>
        /// <param name="texto">Texto de origem</param>
        /// <param name="inicio">Posição inicial (0-based)</param>
        /// <param name="tamanho">Tam<PERSON><PERSON> da substring</param>
        /// <returns>Substring extraída ou string vazia se inválida</returns>
        public static string ExtrairSubstring(string texto, int inicio, int tamanho)
        {
            if (string.IsNullOrEmpty(texto) || inicio < 0 || tamanho <= 0)
                return string.Empty;

            if (inicio >= texto.Length)
                return string.Empty;

            var tamanhoReal = Math.Min(tamanho, texto.Length - inicio);
            return texto.Substring(inicio, tamanhoReal);
        }

        /// <summary>
        /// Remove espaços em branco do início e fim da string
        /// Trata valores nulos de forma segura
        /// </summary>
        /// <param name="valor">Valor a ser limpo</param>
        /// <returns>String limpa ou string vazia se nula</returns>
        public static string LimparEspacos(string valor)
        {
            return valor?.Trim() ?? string.Empty;
        }

        /// <summary>
        /// Remove zeros à esquerda de uma string numérica
        /// Usado para limpar códigos e números
        /// </summary>
        /// <param name="valor">Valor numérico como string</param>
        /// <returns>String sem zeros à esquerda</returns>
        public static string RemoverZerosEsquerda(string valor)
        {
            if (string.IsNullOrWhiteSpace(valor))
                return string.Empty;

            return valor.TrimStart('0');
        }

        /// <summary>
        /// Adiciona zeros à esquerda para atingir tamanho específico
        /// Usado para formatar códigos com tamanho fixo
        /// </summary>
        /// <param name="valor">Valor a ser formatado</param>
        /// <param name="tamanhoTotal">Tamanho total desejado</param>
        /// <returns>String com zeros à esquerda</returns>
        public static string AdicionarZerosEsquerda(string valor, int tamanhoTotal)
        {
            if (string.IsNullOrEmpty(valor))
                valor = "0";

            return valor.PadLeft(tamanhoTotal, '0');
        }

        /// <summary>
        /// Converte string para maiúsculas removendo acentos
        /// Usado para padronizar nomes e textos
        /// </summary>
        /// <param name="texto">Texto a ser convertido</param>
        /// <returns>Texto em maiúsculas sem acentos</returns>
        public static string ConverterParaMaiusculasSemAcentos(string texto)
        {
            if (string.IsNullOrWhiteSpace(texto))
                return string.Empty;

            var textoNormalizado = texto.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in textoNormalizado)
            {
                var categoria = CharUnicodeInfo.GetUnicodeCategory(c);
                if (categoria != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC).ToUpperInvariant();
        }

        /// <summary>
        /// Formata valor monetário para exibição
        /// </summary>
        /// <param name="valor">Valor decimal</param>
        /// <param name="moeda">Símbolo da moeda (padrão: R$)</param>
        /// <returns>Valor formatado como moeda</returns>
        public static string FormatarMoeda(decimal valor, string moeda = "R$")
        {
            return $"{moeda} {valor:N2}";
        }

        /// <summary>
        /// Formata CPF para exibição (XXX.XXX.XXX-XX)
        /// </summary>
        /// <param name="cpf">CPF sem formatação</param>
        /// <returns>CPF formatado</returns>
        public static string FormatarCPF(string cpf)
        {
            if (string.IsNullOrWhiteSpace(cpf) || cpf.Length != 11)
                return cpf;

            return $"{cpf.Substring(0, 3)}.{cpf.Substring(3, 3)}.{cpf.Substring(6, 3)}-{cpf.Substring(9, 2)}";
        }

        /// <summary>
        /// Formata CNPJ para exibição (XX.XXX.XXX/XXXX-XX)
        /// </summary>
        /// <param name="cnpj">CNPJ sem formatação</param>
        /// <returns>CNPJ formatado</returns>
        public static string FormatarCNPJ(string cnpj)
        {
            if (string.IsNullOrWhiteSpace(cnpj) || cnpj.Length != 14)
                return cnpj;

            return $"{cnpj.Substring(0, 2)}.{cnpj.Substring(2, 3)}.{cnpj.Substring(5, 3)}/{cnpj.Substring(8, 4)}-{cnpj.Substring(12, 2)}";
        }

        /// <summary>
        /// Formata telefone para exibição
        /// </summary>
        /// <param name="ddd">Código de área</param>
        /// <param name="numero">Número do telefone</param>
        /// <returns>Telefone formatado</returns>
        public static string FormatarTelefone(string ddd, string numero)
        {
            if (string.IsNullOrWhiteSpace(ddd) || string.IsNullOrWhiteSpace(numero))
                return $"{ddd}{numero}";

            if (numero.Length == 9)
                return $"({ddd}) {numero.Substring(0, 5)}-{numero.Substring(5, 4)}";
            
            if (numero.Length == 8)
                return $"({ddd}) {numero.Substring(0, 4)}-{numero.Substring(4, 4)}";

            return $"({ddd}) {numero}";
        }

        /// <summary>
        /// Trunca string para tamanho máximo adicionando reticências
        /// </summary>
        /// <param name="texto">Texto a ser truncado</param>
        /// <param name="tamanhoMaximo">Tamanho máximo</param>
        /// <returns>Texto truncado</returns>
        public static string TruncarTexto(string texto, int tamanhoMaximo)
        {
            if (string.IsNullOrEmpty(texto) || texto.Length <= tamanhoMaximo)
                return texto;

            return texto.Substring(0, tamanhoMaximo - 3) + "...";
        }

        /// <summary>
        /// Verifica se uma string representa um valor vazio ou padrão
        /// Usado para detectar campos não preenchidos
        /// </summary>
        /// <param name="valor">Valor a ser verificado</param>
        /// <returns>True se é um valor vazio/padrão</returns>
        public static bool IsValorVazio(string valor)
        {
            if (string.IsNullOrWhiteSpace(valor))
                return true;

            // Verifica padrões comuns de valores vazios
            var valorLimpo = valor.Trim();
            return valorLimpo == "0" || 
                   valorLimpo.All(c => c == '0') || 
                   valorLimpo.All(c => c == ' ') ||
                   valorLimpo == "00000000" || // Data vazia comum
                   valorLimpo == "000000000000000"; // Valor monetário vazio comum
        }
    }
}
