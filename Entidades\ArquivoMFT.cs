using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Classe base para representar qualquer arquivo MFT
    /// Contém header, registros e trailer genéricos
    /// </summary>
    /// <typeparam name="T">Tipo do registro do arquivo</typeparam>
    public class ArquivoMFT<T> where T : class
    {
        /// <summary>
        /// Cabeçalho do arquivo MFT
        /// </summary>
        [Required]
        public HeaderArquivo Header { get; set; } = new HeaderArquivo();

        /// <summary>
        /// Lista de registros do arquivo
        /// </summary>
        [Required]
        public List<T> Registros { get; set; } = new List<T>();

        /// <summary>
        /// Trailer do arquivo MFT
        /// </summary>
        [Required]
        public TrailerArquivo Trailer { get; set; } = new TrailerArquivo();

        /// <summary>
        /// Nome do arquivo original
        /// </summary>
        public string NomeArquivo { get; set; } = string.Empty;

        /// <summary>
        /// Caminho completo do arquivo
        /// </summary>
        public string CaminhoArquivo { get; set; } = string.Empty;

        /// <summary>
        /// Data de processamento do arquivo
        /// </summary>
        public DateTime DataProcessamento { get; set; } = DateTime.Now;

        /// <summary>
        /// Indica se o arquivo foi processado com sucesso
        /// </summary>
        public bool ProcessadoComSucesso { get; set; } = false;

        /// <summary>
        /// Lista de erros encontrados durante o processamento
        /// </summary>
        public List<string> Erros { get; set; } = new List<string>();

        /// <summary>
        /// Lista de avisos encontrados durante o processamento
        /// </summary>
        public List<string> Avisos { get; set; } = new List<string>();

        /// <summary>
        /// Obtém a quantidade total de registros no arquivo
        /// </summary>
        /// <returns>Quantidade de registros</returns>
        public int ObterQuantidadeRegistros()
        {
            return Registros.Count;
        }

        /// <summary>
        /// Verifica se a quantidade de registros do trailer confere com a quantidade real
        /// </summary>
        /// <returns>True se confere, False caso contrário</returns>
        public bool ValidarQuantidadeRegistros()
        {
            try
            {
                var quantidadeTrailer = Trailer.ObterQuantidadeRegistros();
                return quantidadeTrailer == Registros.Count;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Verifica se as datas do header e trailer são iguais
        /// </summary>
        /// <returns>True se são iguais, False caso contrário</returns>
        public bool ValidarDatasHeaderTrailer()
        {
            try
            {
                var dataHeader = Header.ObterDataGeracao();
                var dataTrailer = Trailer.ObterDataGeracao();
                return dataHeader.Date == dataTrailer.Date;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Valida o arquivo completo (header, registros e trailer)
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarArquivo()
        {
            var valido = true;
            Erros.Clear();

            // Valida header
            if (!Header.ValidarHeader())
            {
                Erros.Add("Header do arquivo inválido");
                valido = false;
            }

            // Valida trailer
            if (!Trailer.ValidarTrailer())
            {
                Erros.Add("Trailer do arquivo inválido");
                valido = false;
            }

            // Valida quantidade de registros
            if (!ValidarQuantidadeRegistros())
            {
                Erros.Add($"Quantidade de registros não confere. Trailer: {Trailer.QuantidadeRegistros}, Real: {Registros.Count}");
                valido = false;
            }

            // Valida datas
            if (!ValidarDatasHeaderTrailer())
            {
                Erros.Add("Datas do header e trailer são diferentes");
                valido = false;
            }

            return valido;
        }

        /// <summary>
        /// Adiciona um erro à lista de erros
        /// </summary>
        /// <param name="erro">Descrição do erro</param>
        public void AdicionarErro(string erro)
        {
            Erros.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {erro}");
        }

        /// <summary>
        /// Adiciona um aviso à lista de avisos
        /// </summary>
        /// <param name="aviso">Descrição do aviso</param>
        public void AdicionarAviso(string aviso)
        {
            Avisos.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {aviso}");
        }

        /// <summary>
        /// Obtém um resumo do processamento do arquivo
        /// </summary>
        /// <returns>String com resumo do processamento</returns>
        public string ObterResumoProcessamento()
        {
            var resumo = new System.Text.StringBuilder();
            resumo.AppendLine($"Arquivo: {NomeArquivo}");
            resumo.AppendLine($"Caminho: {CaminhoArquivo}");
            resumo.AppendLine($"Data Processamento: {DataProcessamento:yyyy-MM-dd HH:mm:ss}");
            resumo.AppendLine($"Registros: {Registros.Count}");
            resumo.AppendLine($"Processado com Sucesso: {(ProcessadoComSucesso ? "Sim" : "Não")}");
            resumo.AppendLine($"Erros: {Erros.Count}");
            resumo.AppendLine($"Avisos: {Avisos.Count}");

            if (Erros.Count > 0)
            {
                resumo.AppendLine("\nErros:");
                foreach (var erro in Erros)
                {
                    resumo.AppendLine($"  - {erro}");
                }
            }

            if (Avisos.Count > 0)
            {
                resumo.AppendLine("\nAvisos:");
                foreach (var aviso in Avisos)
                {
                    resumo.AppendLine($"  - {aviso}");
                }
            }

            return resumo.ToString();
        }

        /// <summary>
        /// Adiciona um registro à lista de registros
        /// </summary>
        /// <param name="registro">Registro a ser adicionado</param>
        public void AdicionarRegistro(T registro)
        {
            if (registro != null)
            {
                Registros.Add(registro);
            }
        }

        /// <summary>
        /// Obtém o total de registros processados
        /// </summary>
        public int TotalRegistros => Registros.Count;

        /// <summary>
        /// Obtém o número de registros válidos (todos os registros na lista são considerados válidos)
        /// </summary>
        public int RegistrosValidos => Registros.Count;

        /// <summary>
        /// Obtém o total de erros encontrados
        /// </summary>
        public int TotalErros => Erros.Count;
    }
}