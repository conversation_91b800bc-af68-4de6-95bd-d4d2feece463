using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa o cabeçalho padrão de todos os arquivos MFT
    /// Estrutura fixa: Posição 1 = "H", Posições 2-9 = "CYBER   ", etc.
    /// </summary>
    public class HeaderArquivo
    {
        /// <summary>
        /// Identificador do header - sempre "H" na posição 1
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Identificador { get; set; } = "H";

        /// <summary>
        /// Nome do sistema - sempre "CYBER   " nas posições 2-9
        /// </summary>
        [Required]
        [StringLength(8)]
        public string Sistema { get; set; } = "CYBER   ";

        /// <summary>
        /// Nome do arquivo - posições 10-39
        /// </summary>
        [Required]
        [StringLength(30)]
        public string NomeArquivo { get; set; } = string.Empty;

        /// <summary>
        /// Tipo de envio - posição 40
        /// D = Diário, S = Semanal, E = Especial
        /// </summary>
        [Required]
        [StringLength(1)]
        public string TipoEnvio { get; set; } = string.Empty;

        /// <summary>
        /// Data de geração do arquivo - posições 41-48 (formato DDMMYYYY)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataGeracao { get; set; } = string.Empty;

        /// <summary>
        /// Converte a data de geração (string DDMMYYYY) para DateTime
        /// </summary>
        /// <returns>Data de geração convertida para DateTime</returns>
        public DateTime ObterDataGeracao()
        {
            if (string.IsNullOrEmpty(DataGeracao) || DataGeracao.Length != 8)
            {
                throw new ArgumentException("Data de geração deve ter 8 dígitos no formato DDMMYYYY");
            }

            var dia = int.Parse(DataGeracao.Substring(0, 2));
            var mes = int.Parse(DataGeracao.Substring(2, 2));
            var ano = int.Parse(DataGeracao.Substring(4, 4));

            return new DateTime(ano, mes, dia);
        }

        /// <summary>
        /// Define a data de geração a partir de um DateTime
        /// </summary>
        /// <param name="data">Data para converter</param>
        public void DefinirDataGeracao(DateTime data)
        {
            DataGeracao = data.ToString("ddMMyyyy");
        }

        /// <summary>
        /// Valida se o header está correto
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarHeader()
        {
            return Identificador == "H" &&
                   Sistema == "CYBER   " &&
                   !string.IsNullOrEmpty(NomeArquivo) &&
                   (TipoEnvio == "D" || TipoEnvio == "S" || TipoEnvio == "E") &&
                   !string.IsNullOrEmpty(DataGeracao) &&
                   DataGeracao.Length == 8;
        }
    }
} 