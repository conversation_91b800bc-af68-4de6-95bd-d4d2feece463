<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>SistemaMFT</RootNamespace>
    <AssemblyName>SistemaMFT</AssemblyName>
    <AssemblyTitle>Sistema MFT - Processamento de Arquivos e APIs CyberAgreements</AssemblyTitle>
    <Product>Sistema MFT QuintoAndar</Product>
    <Copyright>Copyright © 2024 QuintoAndar</Copyright>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <!-- Pacotes para comunicação SFTP -->
    <PackageReference Include="SSH.NET" Version="2020.0.2" />
    
    <!-- Pacotes para comunicação HTTP/HTTPS -->
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- Pacotes para criptografia GPG -->
	  <PackageReference Include="PgpCore" Version="6.3.0" />

	  <!-- Pacotes para manipulação de arquivos -->
    <PackageReference Include="System.Text.Encoding.CodePages" Version="7.0.0" />
    
    <!-- Pacotes para configuração -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="7.0.0" />
    
    <!-- Pacotes para validação -->
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuracao\chaves\chave_publica.asc">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuracao\chaves\chave_privada.asc">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project> 