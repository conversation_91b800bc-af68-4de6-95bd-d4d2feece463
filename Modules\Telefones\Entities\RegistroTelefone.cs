using System;
using System.ComponentModel.DataAnnotations;
using SistemaMFT.Core.Entities;
using SistemaMFT.Core.Common;

namespace SistemaMFT.Modules.Telefones.Entities
{
    /// <summary>
    /// Entidade que representa um registro do arquivo TELEFONES_OUT.txt
    /// Layout: 77 caracteres por linha (pode ter 121 caracteres em alguns casos)
    /// Código de Registro: "701"
    /// Herda de BaseEntity para manter consistência com outros módulos
    /// </summary>
    public class RegistroTelefone : BaseEntity
    {
        /// <summary>
        /// Tipo de registro - posições 1-3 (valor fixo "701")
        /// Identifica que este é um registro de telefone
        /// </summary>
        [Required]
        [StringLength(3)]
        public string TipoRegistro { get; set; } = "701";

        /// <summary>
        /// Grupo - posição 4 (1=QuintoAndar Locação, 2=QuintoCred Crédito)
        /// Define o tipo de negócio do telefone
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Grupo { get; set; } = string.Empty;

        /// <summary>
        /// Número do contrato - posições 5-29 (25 caracteres)
        /// Identificador do contrato ao qual o telefone pertence
        /// </summary>
        [Required]
        [StringLength(25)]
        public string NumeroContrato { get; set; } = string.Empty;

        /// <summary>
        /// Código de área - posições 30-32 (3 caracteres)
        /// DDD do telefone (11-99)
        /// </summary>
        [Required]
        [StringLength(3)]
        public string CodigoArea { get; set; } = string.Empty;

        /// <summary>
        /// Número do telefone - posições 33-47 (15 caracteres)
        /// Número do telefone sem DDD (8 ou 9 dígitos)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string NumeroTelefone { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do telefone - posição 48 (0=Outro, 1=Casa, 2=Trabalho, 4=Familiar, 5=Celular)
        /// Categoria do telefone
        /// </summary>
        [Required]
        [StringLength(1)]
        public string TipoTelefone { get; set; } = string.Empty;

        /// <summary>
        /// Extensão - posições 49-58 (10 caracteres)
        /// Ramal do telefone (opcional)
        /// </summary>
        [StringLength(10)]
        public string Extensao { get; set; } = string.Empty;

        /// <summary>
        /// Data de criação - posições 59-66 (8 caracteres - formato MMDDAAAA)
        /// Data de cadastro do telefone
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataCriacaoTelefone { get; set; } = string.Empty;

        /// <summary>
        /// Status do telefone - posição 67 (A=Ativo, I=Inativo)
        /// Situação atual do telefone
        /// </summary>
        [Required]
        [StringLength(1)]
        public string StatusTelefone { get; set; } = string.Empty;

        /// <summary>
        /// Ranking - posições 75-77 (3 caracteres)
        /// Prioridade do telefone (1-999)
        /// </summary>
        [StringLength(3)]
        public string Ranking { get; set; } = string.Empty;

        // Propriedades derivadas para facilitar o uso e exibição

        /// <summary>
        /// Descrição do grupo (QuintoAndar - Locação ou QuintoCred - Crédito)
        /// </summary>
        public string GrupoDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Descrição do tipo de telefone
        /// </summary>
        public string TipoDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Descrição do status (Ativo, Inativo)
        /// </summary>
        public string StatusDescricao { get; set; } = string.Empty;

        /// <summary>
        /// Data de criação formatada para exibição (DD/MM/AAAA)
        /// </summary>
        public string DataCriacaoFormatada { get; set; } = string.Empty;

        /// <summary>
        /// Telefone completo formatado para exibição ((XX) XXXXX-XXXX)
        /// </summary>
        public string TelefoneFormatado { get; set; } = string.Empty;

        /// <summary>
        /// Ranking como número inteiro
        /// </summary>
        public int RankingNumerico { get; set; }

        /// <summary>
        /// Indica se o telefone é celular (tipo 5)
        /// </summary>
        public bool IsCelular => TipoTelefone == "5";

        /// <summary>
        /// Indica se o telefone tem extensão
        /// </summary>
        public bool TemExtensao => !string.IsNullOrWhiteSpace(Extensao) && Extensao.Trim() != "0";

        /// <summary>
        /// Converte data de criação (MMDDAAAA) para DateTime
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Data de criação como DateTime ou null se inválida</returns>
        public DateTime? ObterDataCriacao()
        {
            var resultado = ValidationHelper.ValidarDataMMDDAAAA(DataCriacaoTelefone);
            return resultado.valido ? resultado.dataFormatada : null;
        }

        /// <summary>
        /// Obtém o telefone formatado completo
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>Telefone formatado</returns>
        public string ObterTelefoneFormatado()
        {
            if (string.IsNullOrEmpty(CodigoArea) || string.IsNullOrEmpty(NumeroTelefone))
                return string.Empty;

            var numero = NumeroTelefone.Trim();
            var area = CodigoArea.Trim();

            return StringHelper.FormatarTelefone(area, numero);
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras da documentação
        /// Método mantido para compatibilidade com código existente
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return TipoRegistro == "701" &&
                   ValidationHelper.ValidarValoresPermitidos(Grupo, "1", "2") &&
                   ValidationHelper.NaoVazio(NumeroContrato) &&
                   ValidationHelper.NaoVazio(CodigoArea) &&
                   ValidationHelper.NaoVazio(NumeroTelefone) &&
                   ValidationHelper.ValidarValoresPermitidos(TipoTelefone, "0", "1", "2", "4", "5") &&
                   ValidationHelper.ValidarValoresPermitidos(StatusTelefone, "A", "I");
        }

        /// <summary>
        /// Sobrescreve o método base para fornecer resumo específico do telefone
        /// </summary>
        /// <returns>String com resumo do telefone</returns>
        public override string ObterResumo()
        {
            return $"[TELEFONE] {TelefoneFormatado} | Contrato: {NumeroContrato} | Tipo: {TipoDescricao} | Status: {StatusDescricao}";
        }

        /// <summary>
        /// Valida se a entidade atende aos requisitos específicos de telefone
        /// </summary>
        /// <returns>True se a entidade é válida</returns>
        public override bool ValidarBase()
        {
            return base.ValidarBase() && ValidarRegistro();
        }

        /// <summary>
        /// Atualiza as propriedades derivadas com base nos valores dos campos
        /// </summary>
        public void AtualizarPropriedadesDerivadas()
        {
            // Grupo
            GrupoDescricao = Grupo switch
            {
                "1" => "QuintoAndar - Locação",
                "2" => "QuintoCred - Crédito",
                _ => "Grupo Desconhecido"
            };

            // Tipo
            TipoDescricao = TipoTelefone switch
            {
                "0" => "Outro",
                "1" => "Casa",
                "2" => "Trabalho",
                "4" => "Familiar",
                "5" => "Celular",
                _ => "Tipo Desconhecido"
            };

            // Status
            StatusDescricao = StatusTelefone switch
            {
                "A" => "Ativo",
                "I" => "Inativo",
                _ => "Status Desconhecido"
            };

            // Data formatada
            var dataValidacao = ValidationHelper.ValidarDataMMDDAAAA(DataCriacaoTelefone);
            DataCriacaoFormatada = dataValidacao.valido ?
                dataValidacao.dataFormatada?.ToString("dd/MM/yyyy") ?? string.Empty :
                DataCriacaoTelefone;

            // Telefone formatado
            TelefoneFormatado = ObterTelefoneFormatado();

            // Ranking numérico
            if (int.TryParse(Ranking?.Trim(), out int rankingNum))
            {
                RankingNumerico = rankingNum;
            }
        }
    }
}
