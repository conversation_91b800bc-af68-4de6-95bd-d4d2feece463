using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SistemaMFT.Core.Entities;
using SistemaMFT.Modules.Telefones.Entities;
using SistemaMFT.Modules.Telefones.Processors;

namespace SistemaMFT.Modules.Telefones.Services
{
    /// <summary>
    /// Serviço de alto nível para operações com telefones
    /// Coordena o processamento, validação e geração de relatórios
    /// Independente de outros módulos, contém apenas lógica específica de telefones
    /// </summary>
    public class TelefonesService
    {
        private readonly TelefonesProcessor _processor;
        private const string PASTA_SAIDA = "Storage/Output/Telefones";

        /// <summary>
        /// Construtor do serviço de telefones
        /// </summary>
        public TelefonesService()
        {
            _processor = new TelefonesProcessor();
        }

        /// <summary>
        /// Processa um arquivo de telefones e gera relatórios
        /// Método principal que coordena todo o fluxo de processamento
        /// </summary>
        /// <param name="caminhoArquivo">Caminho do arquivo de telefones</param>
        /// <returns>Resultado do processamento com estatísticas</returns>
        public async Task<TelefonesProcessingResult> ProcessarArquivoTelefonesAsync(string caminhoArquivo)
        {
            var resultado = new TelefonesProcessingResult
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                Console.WriteLine($"\n=== INICIANDO PROCESSAMENTO DE TELEFONES ===");
                Console.WriteLine($"Arquivo: {resultado.NomeArquivo}");

                // Validar se o arquivo é de telefones
                if (!_processor.ValidarFormatoArquivo(caminhoArquivo))
                {
                    resultado.Erro = "Arquivo não é um arquivo de telefones válido";
                    return resultado;
                }

                // Processar o arquivo
                var resultadoProcessamento = await _processor.ProcessarArquivoAsync(caminhoArquivo);
                
                // Mapear resultados
                resultado.TelefonesValidos = resultadoProcessamento.RegistrosValidos;
                resultado.TelefonesComErro = resultadoProcessamento.RegistrosComErro;
                resultado.ProcessadoComSucesso = resultadoProcessamento.ProcessadoComSucesso;

                // Gerar estatísticas
                GerarEstatisticas(resultado);

                // Gerar relatórios se houve sucesso
                if (resultado.ProcessadoComSucesso)
                {
                    await GerarRelatoriosAsync(resultado);
                }

                Console.WriteLine($"\n=== PROCESSAMENTO DE TELEFONES CONCLUÍDO ===");
                Console.WriteLine($"Total processado: {resultado.TotalLinhas}");
                Console.WriteLine($"Válidos: {resultado.TotalValidos}");
                Console.WriteLine($"Com erro: {resultado.TotalErros}");
                Console.WriteLine($"Taxa de sucesso: {resultado.PercentualSucesso:F1}%");

                return resultado;
            }
            catch (Exception ex)
            {
                resultado.Erro = $"Erro inesperado: {ex.Message}";
                resultado.ProcessadoComSucesso = false;
                Console.WriteLine($"[ERRO] {resultado.Erro}");
                return resultado;
            }
        }

        /// <summary>
        /// Obtém estatísticas detalhadas dos telefones processados
        /// </summary>
        /// <param name="telefones">Lista de telefones válidos</param>
        /// <returns>Estatísticas dos telefones</returns>
        public TelefonesStatistics ObterEstatisticas(IEnumerable<RegistroTelefone> telefones)
        {
            var listaTelefones = telefones.ToList();
            
            return new TelefonesStatistics
            {
                TotalTelefones = listaTelefones.Count,
                TelefonesAtivos = listaTelefones.Count(t => t.StatusTelefone == "A"),
                TelefonesInativos = listaTelefones.Count(t => t.StatusTelefone == "I"),
                TelefonesLocacao = listaTelefones.Count(t => t.Grupo == "1"),
                TelefonesCredito = listaTelefones.Count(t => t.Grupo == "2"),
                TelefonesCelular = listaTelefones.Count(t => t.TipoTelefone == "5"),
                TelefonesCasa = listaTelefones.Count(t => t.TipoTelefone == "1"),
                TelefonesTrabalho = listaTelefones.Count(t => t.TipoTelefone == "2"),
                TelefonesFamiliar = listaTelefones.Count(t => t.TipoTelefone == "4"),
                TelefonesOutro = listaTelefones.Count(t => t.TipoTelefone == "0"),
                TelefonesComExtensao = listaTelefones.Count(t => t.TemExtensao),
                ContratosUnicos = listaTelefones.Select(t => t.NumeroContrato).Distinct().Count()
            };
        }

        /// <summary>
        /// Exporta telefones válidos para arquivo CSV
        /// </summary>
        /// <param name="telefones">Lista de telefones</param>
        /// <param name="caminhoArquivo">Caminho do arquivo de saída</param>
        /// <returns>Task representando a operação</returns>
        public async Task ExportarTelefonesCSVAsync(IEnumerable<RegistroTelefone> telefones, string caminhoArquivo)
        {
            var csv = new StringBuilder();
            
            // Cabeçalho
            csv.AppendLine("NumeroContrato,Grupo,GrupoDescricao,CodigoArea,NumeroTelefone,TelefoneFormatado,TipoTelefone,TipoDescricao,Extensao,DataCriacaoTelefone,DataCriacaoFormatada,StatusTelefone,StatusDescricao,Ranking,RankingNumerico");

            // Dados
            foreach (var telefone in telefones)
            {
                csv.AppendLine($"\"{telefone.NumeroContrato}\",\"{telefone.Grupo}\",\"{telefone.GrupoDescricao}\",\"{telefone.CodigoArea}\",\"{telefone.NumeroTelefone}\",\"{telefone.TelefoneFormatado}\",\"{telefone.TipoTelefone}\",\"{telefone.TipoDescricao}\",\"{telefone.Extensao}\",\"{telefone.DataCriacaoTelefone}\",\"{telefone.DataCriacaoFormatada}\",\"{telefone.StatusTelefone}\",\"{telefone.StatusDescricao}\",\"{telefone.Ranking}\",{telefone.RankingNumerico}");
            }

            await File.WriteAllTextAsync(caminhoArquivo, csv.ToString(), Encoding.UTF8);
        }

        #region Métodos Privados

        /// <summary>
        /// Gera estatísticas do processamento
        /// </summary>
        private void GerarEstatisticas(TelefonesProcessingResult resultado)
        {
            resultado.Estatisticas = ObterEstatisticas(resultado.TelefonesValidos);
        }

        /// <summary>
        /// Gera relatórios de saída
        /// </summary>
        private async Task GerarRelatoriosAsync(TelefonesProcessingResult resultado)
        {
            try
            {
                // Criar pasta de saída se não existir
                Directory.CreateDirectory(PASTA_SAIDA);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var nomeBase = Path.GetFileNameWithoutExtension(resultado.NomeArquivo);

                // Relatório de telefones válidos
                if (resultado.TelefonesValidos.Any())
                {
                    var caminhoCSV = Path.Combine(PASTA_SAIDA, $"{nomeBase}_TELEFONES_VALIDOS_{timestamp}.csv");
                    await ExportarTelefonesCSVAsync(resultado.TelefonesValidos, caminhoCSV);
                    resultado.ArquivosSaida.Add(caminhoCSV);
                }

                // Relatório de erros
                if (resultado.TelefonesComErro.Any())
                {
                    var caminhoErros = Path.Combine(PASTA_SAIDA, $"{nomeBase}_TELEFONES_ERROS_{timestamp}.txt");
                    await GerarRelatorioErrosAsync(resultado.TelefonesComErro, caminhoErros);
                    resultado.ArquivosSaida.Add(caminhoErros);
                }

                // Relatório de estatísticas
                var caminhoEstatisticas = Path.Combine(PASTA_SAIDA, $"{nomeBase}_TELEFONES_ESTATISTICAS_{timestamp}.txt");
                await GerarRelatorioEstatisticasAsync(resultado.Estatisticas, caminhoEstatisticas);
                resultado.ArquivosSaida.Add(caminhoEstatisticas);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[AVISO] Erro ao gerar relatórios: {ex.Message}");
            }
        }

        /// <summary>
        /// Gera relatório de erros
        /// </summary>
        private async Task GerarRelatorioErrosAsync(IEnumerable<ProcessingResult<RegistroTelefone>> erros, string caminhoArquivo)
        {
            var relatorio = new StringBuilder();
            relatorio.AppendLine("=== RELATÓRIO DE ERROS - TELEFONES ===");
            relatorio.AppendLine($"Data: {DateTime.Now:dd/MM/yyyy HH:mm:ss}");
            relatorio.AppendLine($"Total de erros: {erros.Count()}");
            relatorio.AppendLine();

            foreach (var erro in erros)
            {
                relatorio.AppendLine($"LINHA {erro.NumeroLinha}:");
                relatorio.AppendLine($"  Conteúdo: {erro.LinhaOriginal}");
                relatorio.AppendLine($"  Erros:");
                foreach (var mensagem in erro.Erros)
                {
                    relatorio.AppendLine($"    - {mensagem}");
                }
                relatorio.AppendLine();
            }

            await File.WriteAllTextAsync(caminhoArquivo, relatorio.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// Gera relatório de estatísticas
        /// </summary>
        private async Task GerarRelatorioEstatisticasAsync(TelefonesStatistics stats, string caminhoArquivo)
        {
            var relatorio = new StringBuilder();
            relatorio.AppendLine("=== ESTATÍSTICAS - TELEFONES ===");
            relatorio.AppendLine($"Data: {DateTime.Now:dd/MM/yyyy HH:mm:ss}");
            relatorio.AppendLine();
            
            relatorio.AppendLine("TOTAIS:");
            relatorio.AppendLine($"  Total de telefones: {stats.TotalTelefones:N0}");
            relatorio.AppendLine($"  Telefones ativos: {stats.TelefonesAtivos:N0}");
            relatorio.AppendLine($"  Telefones inativos: {stats.TelefonesInativos:N0}");
            relatorio.AppendLine($"  Contratos únicos: {stats.ContratosUnicos:N0}");
            relatorio.AppendLine();
            
            relatorio.AppendLine("POR TIPO DE NEGÓCIO:");
            relatorio.AppendLine($"  Locação (QuintoAndar): {stats.TelefonesLocacao:N0}");
            relatorio.AppendLine($"  Crédito (QuintoCred): {stats.TelefonesCredito:N0}");
            relatorio.AppendLine();
            
            relatorio.AppendLine("POR TIPO DE TELEFONE:");
            relatorio.AppendLine($"  Celular: {stats.TelefonesCelular:N0}");
            relatorio.AppendLine($"  Casa: {stats.TelefonesCasa:N0}");
            relatorio.AppendLine($"  Trabalho: {stats.TelefonesTrabalho:N0}");
            relatorio.AppendLine($"  Familiar: {stats.TelefonesFamiliar:N0}");
            relatorio.AppendLine($"  Outro: {stats.TelefonesOutro:N0}");
            relatorio.AppendLine();
            
            relatorio.AppendLine("CARACTERÍSTICAS:");
            relatorio.AppendLine($"  Telefones com extensão: {stats.TelefonesComExtensao:N0}");

            await File.WriteAllTextAsync(caminhoArquivo, relatorio.ToString(), Encoding.UTF8);
        }

        #endregion
    }

    /// <summary>
    /// Resultado específico do processamento de telefones
    /// Estende o resultado base com informações específicas de telefones
    /// </summary>
    public class TelefonesProcessingResult
    {
        public string NomeArquivo { get; set; } = string.Empty;
        public string CaminhoArquivo { get; set; } = string.Empty;
        public DateTime DataProcessamento { get; set; }
        public bool ProcessadoComSucesso { get; set; }
        public string Erro { get; set; } = string.Empty;
        
        public List<RegistroTelefone> TelefonesValidos { get; set; } = new List<RegistroTelefone>();
        public List<ProcessingResult<RegistroTelefone>> TelefonesComErro { get; set; } = new List<ProcessingResult<RegistroTelefone>>();
        public TelefonesStatistics Estatisticas { get; set; } = new TelefonesStatistics();
        public List<string> ArquivosSaida { get; set; } = new List<string>();

        public int TotalLinhas => TelefonesValidos.Count + TelefonesComErro.Count;
        public int TotalValidos => TelefonesValidos.Count;
        public int TotalErros => TelefonesComErro.Count;
        public double PercentualSucesso => TotalLinhas > 0 ? (double)TotalValidos / TotalLinhas * 100 : 0;
    }

    /// <summary>
    /// Estatísticas específicas de telefones
    /// </summary>
    public class TelefonesStatistics
    {
        public int TotalTelefones { get; set; }
        public int TelefonesAtivos { get; set; }
        public int TelefonesInativos { get; set; }
        public int TelefonesLocacao { get; set; }
        public int TelefonesCredito { get; set; }
        public int TelefonesCelular { get; set; }
        public int TelefonesCasa { get; set; }
        public int TelefonesTrabalho { get; set; }
        public int TelefonesFamiliar { get; set; }
        public int TelefonesOutro { get; set; }
        public int TelefonesComExtensao { get; set; }
        public int ContratosUnicos { get; set; }
    }
}
