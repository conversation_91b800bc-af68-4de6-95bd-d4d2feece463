using System;
using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa um registro do arquivo CONTRATOS_OUT.txt conforme documentação
    /// Layout: 155 caracteres por linha
    /// Código de Registro: "100"
    /// </summary>
    public class RegistroContrato
    {
        /// <summary>
        /// Tipo de registro - posições 1-3 (valor fixo "100")
        /// </summary>
        [Required]
        [StringLength(3)]
        public string TipoRegistro { get; set; } = "100";

        /// <summary>
        /// Grupo - posição 4 (1=QuintoAndar Locação, 2=QuintoCred Crédito)
        /// </summary>
        [Required]
        [StringLength(1)]
        public string Grupo { get; set; } = string.Empty;

        /// <summary>
        /// Número do contrato - posições 5-29 (25 caracteres)
        /// </summary>
        [Required]
        [StringLength(25)]
        public string NumeroContrato { get; set; } = string.Empty;

        /// <summary>
        /// CPF/CNPJ do cliente - posições 30-43 (14 caracteres)
        /// </summary>
        [Required]
        [StringLength(14)]
        public string CpfCnpj { get; set; } = string.Empty;

        /// <summary>
        /// Nome do cliente - posições 44-123 (80 caracteres)
        /// </summary>
        [Required]
        [StringLength(80)]
        public string NomeCliente { get; set; } = string.Empty;

        /// <summary>
        /// Status do contrato - posição 124 (A=Ativo, I=Inativo, C=Cancelado)
        /// </summary>
        [Required]
        [StringLength(1)]
        public string StatusContrato { get; set; } = string.Empty;

        /// <summary>
        /// Data de início - posições 125-132 (8 caracteres - formato MMDDAAAA)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataInicio { get; set; } = string.Empty;

        /// <summary>
        /// Data de fim - posições 133-140 (8 caracteres - formato MMDDAAAA)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataFim { get; set; } = string.Empty;

        /// <summary>
        /// Valor do contrato - posições 141-155 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorContrato { get; set; } = string.Empty;

        // Propriedades derivadas para facilitar o uso
        public string TipoDocumento { get; set; } = string.Empty;
        public string DocumentoFormatado { get; set; } = string.Empty;
        public string GrupoDescricao { get; set; } = string.Empty;
        public string StatusDescricao { get; set; } = string.Empty;
        public string DataInicioFormatada { get; set; } = string.Empty;
        public string DataFimFormatada { get; set; } = string.Empty;
        public decimal ValorContratoDecimal { get; set; }
        public string ValorContratoFormatado { get; set; } = string.Empty;

        /// <summary>
        /// Converte valor do contrato para decimal (divide por 100 para obter centavos)
        /// </summary>
        /// <returns>Valor do contrato como decimal</returns>
        public decimal ObterValorContrato()
        {
            if (string.IsNullOrEmpty(ValorContrato))
                return 0;

            var valor = long.Parse(ValorContrato.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte data de início (MMDDAAAA) para DateTime
        /// </summary>
        /// <returns>Data de início como DateTime ou null se inválida</returns>
        public DateTime? ObterDataInicio()
        {
            if (string.IsNullOrEmpty(DataInicio) || DataInicio.Length != 8 || DataInicio == "00000000")
                return null;

            try
            {
                var mes = int.Parse(DataInicio.Substring(0, 2));
                var dia = int.Parse(DataInicio.Substring(2, 2));
                var ano = int.Parse(DataInicio.Substring(4, 4));
                return new DateTime(ano, mes, dia);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Converte data de fim (MMDDAAAA) para DateTime
        /// </summary>
        /// <returns>Data de fim como DateTime ou null se inválida</returns>
        public DateTime? ObterDataFim()
        {
            if (string.IsNullOrEmpty(DataFim) || DataFim.Length != 8 || DataFim == "00000000")
                return null;

            try
            {
                var mes = int.Parse(DataFim.Substring(0, 2));
                var dia = int.Parse(DataFim.Substring(2, 2));
                var ano = int.Parse(DataFim.Substring(4, 4));
                return new DateTime(ano, mes, dia);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras da documentação
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return TipoRegistro == "100" &&
                   (Grupo == "1" || Grupo == "2") &&
                   !string.IsNullOrEmpty(NumeroContrato) &&
                   !string.IsNullOrEmpty(CpfCnpj) &&
                   !string.IsNullOrEmpty(NomeCliente) &&
                   (StatusContrato == "A" || StatusContrato == "I" || StatusContrato == "C");
        }
    }
} 