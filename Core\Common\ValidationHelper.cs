using System;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace SistemaMFT.Core.Common
{
    /// <summary>
    /// Utilitários de validação compartilhados entre todos os módulos
    /// Contém validações básicas que são reutilizadas por diferentes processadores
    /// Evita duplicação de código mantendo independência entre módulos
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// Valida se uma string contém apenas dígitos numéricos
        /// Usado para validar códigos, números de telefone, etc.
        /// </summary>
        /// <param name="valor">Valor a ser validado</param>
        /// <returns>True se contém apenas dígitos</returns>
        public static bool IsNumerico(string valor)
        {
            return !string.IsNullOrWhiteSpace(valor) && valor.All(char.IsDigit);
        }

        /// <summary>
        /// Valida se uma string está dentro de um comprimento específico
        /// Usado para validar campos de tamanho fixo
        /// </summary>
        /// <param name="valor">Valor a ser validado</param>
        /// <param name="tamanhoExato">Tamanho exato esperado</param>
        /// <returns>True se o tamanho está correto</returns>
        public static bool ValidarTamanho(string valor, int tamanhoExato)
        {
            return valor?.Length == tamanhoExato;
        }

        /// <summary>
        /// Valida se uma string está dentro de uma faixa de tamanhos
        /// </summary>
        /// <param name="valor">Valor a ser validado</param>
        /// <param name="tamanhoMinimo">Tamanho mínimo</param>
        /// <param name="tamanhoMaximo">Tamanho máximo</param>
        /// <returns>True se o tamanho está na faixa</returns>
        public static bool ValidarTamanho(string valor, int tamanhoMinimo, int tamanhoMaximo)
        {
            var tamanho = valor?.Length ?? 0;
            return tamanho >= tamanhoMinimo && tamanho <= tamanhoMaximo;
        }

        /// <summary>
        /// Valida se um valor numérico está dentro de uma faixa
        /// </summary>
        /// <param name="valor">Valor a ser validado</param>
        /// <param name="minimo">Valor mínimo</param>
        /// <param name="maximo">Valor máximo</param>
        /// <returns>True se está na faixa</returns>
        public static bool ValidarFaixa(int valor, int minimo, int maximo)
        {
            return valor >= minimo && valor <= maximo;
        }

        /// <summary>
        /// Valida se um valor está em uma lista de valores permitidos
        /// </summary>
        /// <param name="valor">Valor a ser validado</param>
        /// <param name="valoresPermitidos">Array de valores permitidos</param>
        /// <returns>True se o valor está na lista</returns>
        public static bool ValidarValoresPermitidos(string valor, params string[] valoresPermitidos)
        {
            return valoresPermitidos.Contains(valor);
        }

        /// <summary>
        /// Valida se uma string não está vazia ou contém apenas espaços
        /// </summary>
        /// <param name="valor">Valor a ser validado</param>
        /// <returns>True se não está vazio</returns>
        public static bool NaoVazio(string valor)
        {
            return !string.IsNullOrWhiteSpace(valor);
        }

        /// <summary>
        /// Valida formato de data MMDDAAAA
        /// Usado pelos módulos que processam datas neste formato
        /// </summary>
        /// <param name="data">Data no formato MMDDAAAA</param>
        /// <returns>Resultado da validação com data formatada</returns>
        public static (bool valido, string erro, DateTime? dataFormatada) ValidarDataMMDDAAAA(string data)
        {
            if (string.IsNullOrWhiteSpace(data))
                return (false, "Data não pode estar vazia", null);

            if (data.Length != 8)
                return (false, $"Data deve ter 8 dígitos, encontrados {data.Length}", null);

            if (!IsNumerico(data))
                return (false, "Data deve conter apenas números", null);

            var mes = data.Substring(0, 2);
            var dia = data.Substring(2, 2);
            var ano = data.Substring(4, 4);

            if (!int.TryParse(mes, out int mesInt) || mesInt < 1 || mesInt > 12)
                return (false, $"Mês inválido: {mes}", null);

            if (!int.TryParse(dia, out int diaInt) || diaInt < 1 || diaInt > 31)
                return (false, $"Dia inválido: {dia}", null);

            if (!int.TryParse(ano, out int anoInt) || anoInt < 1900 || anoInt > 2100)
                return (false, $"Ano inválido: {ano}", null);

            try
            {
                var dataFormatada = new DateTime(anoInt, mesInt, diaInt);
                return (true, string.Empty, dataFormatada);
            }
            catch (ArgumentOutOfRangeException)
            {
                return (false, $"Data inválida: {dia}/{mes}/{ano}", null);
            }
        }

        /// <summary>
        /// Valida valor monetário com casas decimais implícitas
        /// </summary>
        /// <param name="valor">Valor como string</param>
        /// <param name="casasDecimais">Número de casas decimais implícitas</param>
        /// <returns>Resultado da validação com valor formatado</returns>
        public static (bool valido, string erro, decimal? valorFormatado) ValidarValorMonetario(string valor, int casasDecimais)
        {
            if (string.IsNullOrWhiteSpace(valor))
                return (false, "Valor monetário não pode estar vazio", null);

            if (!IsNumerico(valor))
                return (false, "Valor monetário deve conter apenas números", null);

            if (!decimal.TryParse(valor, out decimal valorDecimal))
                return (false, "Valor monetário inválido", null);

            // Aplica casas decimais implícitas
            var valorFormatado = valorDecimal / (decimal)Math.Pow(10, casasDecimais);

            return (true, string.Empty, valorFormatado);
        }

        /// <summary>
        /// Valida CPF usando algoritmo de dígitos verificadores
        /// </summary>
        /// <param name="cpf">CPF a ser validado</param>
        /// <returns>Resultado da validação</returns>
        public static (bool valido, string erro) ValidarCPF(string cpf)
        {
            if (string.IsNullOrWhiteSpace(cpf))
                return (false, "CPF não pode estar vazio");

            // Remove formatação
            cpf = Regex.Replace(cpf, @"[^\d]", "");

            if (cpf.Length != 11)
                return (false, "CPF deve ter 11 dígitos");

            // Verifica se todos os dígitos são iguais
            if (cpf.All(c => c == cpf[0]))
                return (false, "CPF inválido - todos os dígitos são iguais");

            // Calcula primeiro dígito verificador
            var soma = 0;
            for (int i = 0; i < 9; i++)
                soma += int.Parse(cpf[i].ToString()) * (10 - i);

            var resto = soma % 11;
            var digito1 = resto < 2 ? 0 : 11 - resto;

            if (int.Parse(cpf[9].ToString()) != digito1)
                return (false, "CPF inválido - primeiro dígito verificador");

            // Calcula segundo dígito verificador
            soma = 0;
            for (int i = 0; i < 10; i++)
                soma += int.Parse(cpf[i].ToString()) * (11 - i);

            resto = soma % 11;
            var digito2 = resto < 2 ? 0 : 11 - resto;

            if (int.Parse(cpf[10].ToString()) != digito2)
                return (false, "CPF inválido - segundo dígito verificador");

            return (true, string.Empty);
        }

        /// <summary>
        /// Valida CNPJ usando algoritmo de dígitos verificadores
        /// </summary>
        /// <param name="cnpj">CNPJ a ser validado</param>
        /// <returns>Resultado da validação</returns>
        public static (bool valido, string erro) ValidarCNPJ(string cnpj)
        {
            if (string.IsNullOrWhiteSpace(cnpj))
                return (false, "CNPJ não pode estar vazio");

            // Remove formatação
            cnpj = Regex.Replace(cnpj, @"[^\d]", "");

            if (cnpj.Length != 14)
                return (false, "CNPJ deve ter 14 dígitos");

            // Verifica se todos os dígitos são iguais
            if (cnpj.All(c => c == cnpj[0]))
                return (false, "CNPJ inválido - todos os dígitos são iguais");

            // Calcula primeiro dígito verificador
            var multiplicadores1 = new int[] { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            var soma = 0;
            for (int i = 0; i < 12; i++)
                soma += int.Parse(cnpj[i].ToString()) * multiplicadores1[i];

            var resto = soma % 11;
            var digito1 = resto < 2 ? 0 : 11 - resto;

            if (int.Parse(cnpj[12].ToString()) != digito1)
                return (false, "CNPJ inválido - primeiro dígito verificador");

            // Calcula segundo dígito verificador
            var multiplicadores2 = new int[] { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            soma = 0;
            for (int i = 0; i < 13; i++)
                soma += int.Parse(cnpj[i].ToString()) * multiplicadores2[i];

            resto = soma % 11;
            var digito2 = resto < 2 ? 0 : 11 - resto;

            if (int.Parse(cnpj[13].ToString()) != digito2)
                return (false, "CNPJ inválido - segundo dígito verificador");

            return (true, string.Empty);
        }

        /// <summary>
        /// Valida código de área telefônica (11-99)
        /// </summary>
        /// <param name="codigoArea">Código de área a ser validado</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro, string codigoArea, string descricao) ValidarCodigoArea(string codigoArea)
        {
            if (string.IsNullOrWhiteSpace(codigoArea))
                return (false, "Código de área não pode estar vazio", string.Empty, string.Empty);

            if (!int.TryParse(codigoArea.Trim(), out int codigo))
                return (false, "Código de área deve ser numérico", string.Empty, string.Empty);

            if (codigo < 11 || codigo > 99)
                return (false, "Código de área deve estar entre 11 e 99", string.Empty, string.Empty);

            return (true, string.Empty, codigo.ToString(), $"DDD {codigo}");
        }

        /// <summary>
        /// Valida número de telefone (8 ou 9 dígitos)
        /// </summary>
        /// <param name="numeroTelefone">Número de telefone a ser validado</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro, string numero, string descricao) ValidarNumeroTelefone(string numeroTelefone)
        {
            if (string.IsNullOrWhiteSpace(numeroTelefone))
                return (false, "Número de telefone não pode estar vazio", string.Empty, string.Empty);

            // Remove caracteres não numéricos
            var numeroLimpo = new string(numeroTelefone.Where(char.IsDigit).ToArray());

            if (numeroLimpo.Length != 8 && numeroLimpo.Length != 9)
                return (false, "Número de telefone deve ter 8 ou 9 dígitos", string.Empty, string.Empty);

            var descricao = numeroLimpo.Length == 9 ? "Celular (9 dígitos)" : "Fixo (8 dígitos)";
            return (true, string.Empty, numeroLimpo, descricao);
        }

        /// <summary>
        /// Valida ranking (1-999)
        /// </summary>
        /// <param name="ranking">Ranking a ser validado</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro, string ranking, string descricao) ValidarRanking(string ranking)
        {
            if (string.IsNullOrWhiteSpace(ranking))
                return (false, "Ranking não pode estar vazio", string.Empty, string.Empty);

            if (!int.TryParse(ranking.Trim(), out int rank))
                return (false, "Ranking deve ser numérico", string.Empty, string.Empty);

            if (rank < 1 || rank > 999)
                return (false, "Ranking deve estar entre 1 e 999", string.Empty, string.Empty);

            return (true, string.Empty, rank.ToString(), $"Prioridade {rank}");
        }

        /// <summary>
        /// Valida status com valores permitidos
        /// </summary>
        /// <param name="status">Status a ser validado</param>
        /// <param name="valoresPermitidos">Array de valores permitidos</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro) ValidarStatus(string status, params string[] valoresPermitidos)
        {
            if (string.IsNullOrWhiteSpace(status))
                return (false, "Status não pode estar vazio");

            var statusLimpo = status.Trim();

            foreach (var valorPermitido in valoresPermitidos)
            {
                if (statusLimpo.Equals(valorPermitido, StringComparison.OrdinalIgnoreCase))
                    return (true, string.Empty);
            }

            return (false, $"Status '{status}' não é válido. Valores permitidos: {string.Join(", ", valoresPermitidos)}");
        }
    }
}
