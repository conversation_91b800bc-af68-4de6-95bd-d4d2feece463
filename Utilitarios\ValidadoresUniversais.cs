using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq; // Added for .Where()

namespace SistemaMFT.Utilitarios
{
    public static class ValidadoresUniversais
    {
        /// <summary>
        /// Valida CPF usando algoritmo completo conforme documentação
        /// </summary>
        public static (bool valido, string erro, string cpfFormatado) ValidarCPF(string cpf)
        {
            // Remove todos os caracteres não numéricos
            var cpfLimpo = new string(cpf.Where(char.IsDigit).ToArray());

            // Verifica se tem 11 dígitos
            if (cpfLimpo.Length != 11)
                return (false, $"CPF deve ter 11 dígitos, encontrados {cpfLimpo.Length}", null);

            // Verifica se não são todos iguais (111.111.111-11, etc.)
            if (cpfLimpo.All(c => c == cpfLimpo[0]))
                return (false, "CPF não pode ter todos os dígitos iguais", null);

            // Calcula primeiro dígito verificador
            int soma = 0;
            for (int i = 0; i < 9; i++)
                soma += int.Parse(cpfLimpo[i].ToString()) * (10 - i);

            int digito1 = 11 - (soma % 11);
            if (digito1 >= 10) digito1 = 0;

            // Calcula segundo dígito verificador
            soma = 0;
            for (int i = 0; i < 10; i++)
                soma += int.Parse(cpfLimpo[i].ToString()) * (11 - i);

            int digito2 = 11 - (soma % 11);
            if (digito2 >= 10) digito2 = 0;

            // Verifica se os dígitos calculados conferem
            bool digitosCorretos = (int.Parse(cpfLimpo[9].ToString()) == digito1 &&
                                   int.Parse(cpfLimpo[10].ToString()) == digito2);

            if (!digitosCorretos)
                return (false, $"Dígitos verificadores inválidos. Esperado: {digito1}{digito2}, Encontrado: {cpfLimpo[9]}{cpfLimpo[10]}", null);

            var cpfFormatado = $"{cpfLimpo.Substring(0, 3)}.{cpfLimpo.Substring(3, 3)}.{cpfLimpo.Substring(6, 3)}-{cpfLimpo.Substring(9, 2)}";
            return (true, null, cpfFormatado);
        }

        /// <summary>
        /// Valida CNPJ usando algoritmo completo conforme documentação
        /// </summary>
        public static (bool valido, string erro, string cnpjFormatado) ValidarCNPJ(string cnpj)
        {
            var cnpjLimpo = new string(cnpj.Where(char.IsDigit).ToArray());

            if (cnpjLimpo.Length != 14)
                return (false, $"CNPJ deve ter 14 dígitos, encontrados {cnpjLimpo.Length}", null);

            if (cnpjLimpo.All(c => c == cnpjLimpo[0]))
                return (false, "CNPJ não pode ter todos os dígitos iguais", null);

            // Primeiro dígito verificador
            int soma = 0;
            int peso = 2;
            for (int i = 11; i >= 0; i--)
            {
                soma += int.Parse(cnpjLimpo[i].ToString()) * peso;
                peso = peso == 9 ? 2 : peso + 1;
            }
            int digito1 = soma % 11 < 2 ? 0 : 11 - (soma % 11);

            // Segundo dígito verificador
            soma = 0;
            peso = 2;
            for (int i = 12; i >= 0; i--)
            {
                soma += int.Parse(cnpjLimpo[i].ToString()) * peso;
                peso = peso == 9 ? 2 : peso + 1;
            }
            int digito2 = soma % 11 < 2 ? 0 : 11 - (soma % 11);

            bool digitosCorretos = (int.Parse(cnpjLimpo[12].ToString()) == digito1 &&
                                   int.Parse(cnpjLimpo[13].ToString()) == digito2);

            if (!digitosCorretos)
                return (false, $"Dígitos verificadores inválidos. Esperado: {digito1}{digito2}, Encontrado: {cnpjLimpo[12]}{cnpjLimpo[13]}", null);

            var cnpjFormatado = $"{cnpjLimpo.Substring(0, 2)}.{cnpjLimpo.Substring(2, 3)}.{cnpjLimpo.Substring(5, 3)}/{cnpjLimpo.Substring(8, 4)}-{cnpjLimpo.Substring(12, 2)}";
            return (true, null, cnpjFormatado);
        }
        public static (bool valido, string erro, string dataFormatada, string dataISO, bool dataNula) ValidarDataMMDDAAAA(string dataString)
        {
            if (string.IsNullOrWhiteSpace(dataString) || dataString.Length != 8)
                return (false, $"Data deve ter 8 dígitos, encontrados {(dataString == null ? 0 : dataString.Length)}", null, null, false);
            if (dataString == "00000000")
                return (true, null, null, null, true);
            int mes = int.Parse(dataString.Substring(0, 2));
            int dia = int.Parse(dataString.Substring(2, 2));
            int ano = int.Parse(dataString.Substring(4, 4));
            if (mes < 1 || mes > 12)
                return (false, $"Mês inválido: {mes}. Deve estar entre 1 e 12", null, null, false);
            if (dia < 1 || dia > 31)
                return (false, $"Dia inválido: {dia}. Deve estar entre 1 e 31", null, null, false);
            if (ano < 1900 || ano > 2100)
                return (false, $"Ano inválido: {ano}. Deve estar entre 1900 e 2100", null, null, false);
            DateTime dataObj;
            if (!DateTime.TryParseExact($"{ano}-{mes:00}-{dia:00}", "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataObj))
                return (false, $"Data inexistente: {dia}/{mes}/{ano}", null, null, false);
            return (true, null, $"{dia:00}/{mes:00}/{ano}", $"{ano}-{mes:00}-{dia:00}", false);
        }

        public static (bool valido, string erro, decimal valorDecimal, string valorFormatado, bool valorNulo, bool valorZero) ValidarValorMonetario(string valorString, int decimaisImplicitas = 2)
        {
            if (string.IsNullOrWhiteSpace(valorString))
                return (true, null, 0, "R$ 0,00", true, true);
            var valorLimpo = new string(valorString.Where(char.IsDigit).ToArray());
            if (string.IsNullOrEmpty(valorLimpo) || valorLimpo.Trim('0').Length == 0)
                return (true, null, 0, "R$ 0,00", false, true);
            if (!long.TryParse(valorLimpo, out long valorNumerico))
                return (false, $"Valor monetário inválido: {valorString}", 0, null, false, false);
            decimal valorDecimal = valorNumerico / (decimal)Math.Pow(10, decimaisImplicitas);
            if (valorDecimal < 0)
                return (false, $"Valor monetário não pode ser negativo: {valorDecimal}", valorDecimal, null, false, false);
            return (true, null, valorDecimal, valorDecimal.ToString("C", new CultureInfo("pt-BR")), false, valorDecimal == 0);
        }

        public static (bool valido, string erro, string status, string descricao) ValidarStatus(string status, string[] permitidos, Dictionary<string, string> descricoes)
        {
            if (string.IsNullOrWhiteSpace(status))
                return (false, "Status não pode estar vazio", null, null);
            var statusLimpo = status.Trim().ToUpperInvariant();
            if (Array.IndexOf(permitidos, statusLimpo) < 0)
                return (false, $"Status inválido: '{statusLimpo}'. Valores permitidos: {string.Join(", ", permitidos)}", null, null);
            return (true, null, statusLimpo, descricoes != null && descricoes.ContainsKey(statusLimpo) ? descricoes[statusLimpo] : "Descrição não disponível");
        }

        /// <summary>
        /// Valida código de área telefônica (11-99)
        /// </summary>
        /// <param name="codigoArea">Código de área a ser validado</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro, string codigoArea, string descricao) ValidarCodigoArea(string codigoArea)
        {
            if (string.IsNullOrWhiteSpace(codigoArea))
                return (false, "Código de área não pode estar vazio", null, null);

            if (!int.TryParse(codigoArea.Trim(), out int codigo))
                return (false, "Código de área deve ser numérico", null, null);

            if (codigo < 11 || codigo > 99)
                return (false, "Código de área deve estar entre 11 e 99", null, null);

            return (true, null, codigoArea.Trim(), "Código de área válido");
        }

        /// <summary>
        /// Valida número de telefone (8 ou 9 dígitos)
        /// </summary>
        /// <param name="numeroTelefone">Número de telefone a ser validado</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro, string numero, string descricao) ValidarNumeroTelefone(string numeroTelefone)
        {
            if (string.IsNullOrWhiteSpace(numeroTelefone))
                return (false, "Número de telefone não pode estar vazio", null, null);

            // Remove caracteres não numéricos
            var numeroLimpo = new string(numeroTelefone.Where(char.IsDigit).ToArray());

            if (numeroLimpo.Length != 8 && numeroLimpo.Length != 9)
                return (false, "Número de telefone deve ter 8 ou 9 dígitos", null, null);

            // Se tem 9 dígitos, deve começar com 9
            if (numeroLimpo.Length == 9 && !numeroLimpo.StartsWith("9"))
                return (false, "Número de telefone com 9 dígitos deve começar com 9", null, null);

            return (true, null, numeroLimpo, "Número de telefone válido");
        }

        /// <summary>
        /// Valida ranking (1-999)
        /// </summary>
        /// <param name="ranking">Ranking a ser validado</param>
        /// <returns>Tupla com resultado da validação e mensagem</returns>
        public static (bool valido, string erro, string ranking, string descricao) ValidarRanking(string ranking)
        {
            if (string.IsNullOrWhiteSpace(ranking))
                return (false, "Ranking não pode estar vazio", null, null);

            if (!int.TryParse(ranking.Trim(), out int rank))
                return (false, "Ranking deve ser numérico", null, null);

            if (rank < 1 || rank > 999)
                return (false, "Ranking deve estar entre 1 e 999", null, null);

            return (true, null, ranking.Trim(), "Ranking válido");
        }
    }
}