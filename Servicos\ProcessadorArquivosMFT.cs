using Microsoft.Extensions.Logging;
using SistemaMFT.Configuracao;
using SistemaMFT.Entidades;
using SistemaMFT.Utilitarios;

namespace SistemaMFT.Servicos
{
    /// <summary>
    /// Serviço responsável pelo processamento de arquivos MFT
    /// Realiza leitura, validação e conversão de arquivos para entidades tipadas
    /// </summary>
    public class ProcessadorArquivosMFT
    {
        private readonly ConfiguracaoMFT _configuracao;
        private readonly ILogger<ProcessadorArquivosMFT> _logger;

        public ProcessadorArquivosMFT(ConfiguracaoMFT configuracao, ILogger<ProcessadorArquivosMFT> logger)
        {
            _configuracao = configuracao ?? throw new ArgumentNullException(nameof(configuracao));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Processa um arquivo de contratos (CONTRATOS_OUT.txt)
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Arquivo MFT processado com registros de contrato</returns>
        public async Task<ArquivoMFT<RegistroContrato>> ProcessarArquivoContratosAsync(string caminhoArquivo)
        {
            _logger.LogInformation("Iniciando processamento do arquivo de contratos: {Arquivo}", caminhoArquivo);

            var arquivo = new ArquivoMFT<RegistroContrato>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                // Lê todas as linhas do arquivo
                var linhas = await Task.Run(() => ManipuladorArquivos.LerTodasLinhas(caminhoArquivo));

                if (linhas.Length < 2)
                {
                    arquivo.AdicionarErro("Arquivo deve ter pelo menos header e trailer");
                    return arquivo;
                }

                // Processa header
                arquivo.Header = ProcessarHeader(linhas[0]);

                // Processa trailer
                arquivo.Trailer = ProcessarTrailer(linhas[^1]);

                // Processa registros (todas as linhas exceto primeira e última)
                for (int i = 1; i < linhas.Length - 1; i++)
                {
                    try
                    {
                        var registro = ProcessadorContratos.ProcessarLinhaContrato(linhas[i], i + 1);
                        if (registro != null)
                        {
                            arquivo.Registros.Add(registro);
                        }
                        else
                        {
                            arquivo.AdicionarErro($"Linha {i + 1} não pôde ser processada - falha na validação");
                        }
                    }
                    catch (Exception ex)
                    {
                        arquivo.AdicionarErro($"Erro ao processar linha {i + 1}: {ex.Message}");
                    }
                }

                // Valida arquivo
                arquivo.ProcessadoComSucesso = arquivo.ValidarArquivo();

                _logger.LogInformation("Processamento concluído. Registros: {Registros}, Erros: {Erros}", 
                    arquivo.Registros.Count, arquivo.Erros.Count);

                return arquivo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao processar arquivo de contratos: {Arquivo}", caminhoArquivo);
                arquivo.AdicionarErro($"Erro geral no processamento: {ex.Message}");
                return arquivo;
            }
        }



        /// <summary>
        /// Processa um arquivo de relações (RELATIONS_OUT.txt)
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Arquivo MFT processado com registros de relação</returns>
        public async Task<ArquivoMFT<RegistroRelacao>> ProcessarArquivoRelacoesAsync(string caminhoArquivo)
        {
            _logger.LogInformation("Iniciando processamento do arquivo de relações: {Arquivo}", caminhoArquivo);

            var arquivo = new ArquivoMFT<RegistroRelacao>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                var linhas = await Task.Run(() => ManipuladorArquivos.LerTodasLinhas(caminhoArquivo));

                if (linhas.Length < 2)
                {
                    arquivo.AdicionarErro("Arquivo deve ter pelo menos header e trailer");
                    return arquivo;
                }

                arquivo.Header = ProcessarHeader(linhas[0]);
                arquivo.Trailer = ProcessarTrailer(linhas[^1]);

                for (int i = 1; i < linhas.Length - 1; i++)
                {
                    try
                    {
                        var registro = ProcessarRegistroRelacao(linhas[i]);
                        arquivo.Registros.Add(registro);
                    }
                    catch (Exception ex)
                    {
                        arquivo.AdicionarErro($"Erro ao processar linha {i + 1}: {ex.Message}");
                    }
                }

                arquivo.ProcessadoComSucesso = arquivo.ValidarArquivo();

                _logger.LogInformation("Processamento concluído. Registros: {Registros}, Erros: {Erros}", 
                    arquivo.Registros.Count, arquivo.Erros.Count);

                return arquivo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao processar arquivo de relações: {Arquivo}", caminhoArquivo);
                arquivo.AdicionarErro($"Erro geral no processamento: {ex.Message}");
                return arquivo;
            }
        }

        /// <summary>
        /// Processa um arquivo de acordos (ACORDOS_OUT.txt)
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Arquivo MFT processado com registros de acordo</returns>
        public async Task<ArquivoMFT<RegistroAcordo>> ProcessarArquivoAcordosAsync(string caminhoArquivo)
        {
            _logger.LogInformation("Iniciando processamento do arquivo de acordos: {Arquivo}", caminhoArquivo);

            var arquivo = new ArquivoMFT<RegistroAcordo>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                var linhas = await Task.Run(() => ManipuladorArquivos.LerTodasLinhas(caminhoArquivo));

                if (linhas.Length < 2)
                {
                    arquivo.AdicionarErro("Arquivo deve ter pelo menos header e trailer");
                    return arquivo;
                }

                arquivo.Header = ProcessarHeader(linhas[0]);
                arquivo.Trailer = ProcessarTrailer(linhas[^1]);

                for (int i = 1; i < linhas.Length - 1; i++)
                {
                    try
                    {
                        var registro = ProcessarRegistroAcordo(linhas[i]);
                        arquivo.Registros.Add(registro);
                    }
                    catch (Exception ex)
                    {
                        arquivo.AdicionarErro($"Erro ao processar linha {i + 1}: {ex.Message}");
                    }
                }

                arquivo.ProcessadoComSucesso = arquivo.ValidarArquivo();

                _logger.LogInformation("Processamento concluído. Registros: {Registros}, Erros: {Erros}", 
                    arquivo.Registros.Count, arquivo.Erros.Count);

                return arquivo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao processar arquivo de acordos: {Arquivo}", caminhoArquivo);
                arquivo.AdicionarErro($"Erro geral no processamento: {ex.Message}");
                return arquivo;
            }
        }

        /// <summary>
        /// Processa o header de um arquivo MFT
        /// </summary>
        /// <param name="linha">Linha do header</param>
        /// <returns>Header processado</returns>
        private HeaderArquivo ProcessarHeader(string linha)
        {
            var header = new HeaderArquivo();

            if (linha.Length < 48)
            {
                throw new ArgumentException("Linha de header muito curta");
            }

            header.Identificador = linha.Substring(0, 1);
            header.Sistema = linha.Length > 8 ? linha.Substring(1, 8) : linha.Substring(1);
            header.NomeArquivo = linha.Length > 39 ? linha.Substring(9, 30).Trim() : string.Empty;
            header.TipoEnvio = linha.Length > 39 ? linha.Substring(39, 1) : string.Empty;
            header.DataGeracao = linha.Length > 47 ? linha.Substring(40, 8) : string.Empty;

            return header;
        }

        /// <summary>
        /// Processa o trailer de um arquivo MFT
        /// </summary>
        /// <param name="linha">Linha do trailer</param>
        /// <returns>Trailer processado</returns>
        private TrailerArquivo ProcessarTrailer(string linha)
        {
            var trailer = new TrailerArquivo();

            if (linha.Length < 17)
            {
                throw new ArgumentException("Linha de trailer muito curta");
            }

            trailer.Identificador = linha.Substring(0, 1);
            trailer.DataGeracao = linha.Length > 8 ? linha.Substring(1, 8) : string.Empty;
            trailer.QuantidadeRegistros = linha.Length > 16 ? linha.Substring(9, 8) : string.Empty;

            return trailer;
        }



        /// <summary>
        /// Processa uma linha de registro de parcela
        /// </summary>
        /// <param name="linha">Linha do registro</param>
        /// <returns>Registro de parcela processado</returns>
        private RegistroParcela ProcessarRegistroParcela(string linha)
        {
            if (linha.Length < 228)
            {
                throw new ArgumentException("Linha de parcela muito curta");
            }

            var registro = new RegistroParcela
            {
                TipoRegistro = linha.Substring(0, 3),
                Grupo = linha.Substring(3, 1),
                ChaveCyber = linha.Substring(4, 25).Trim(),
                IdParcela = linha.Substring(29, 15).Trim(),
                Status = linha.Substring(44, 1),
                ValorPrincipal = linha.Substring(45, 15).Trim(),
                ValorJuros = linha.Substring(60, 15).Trim(),
                ValorMulta = linha.Substring(75, 15).Trim(),
                DataVencimento = linha.Substring(90, 8).Trim(),
                ValorCustas = linha.Substring(98, 15).Trim(),
                ValorHonorarios = linha.Substring(113, 15).Trim(),
                StatusFatura = linha.Substring(128, 50).Trim(),
                TipoDivida = linha.Substring(178, 50).Trim()
            };

            return registro;
        }

        /// <summary>
        /// Processa uma linha de registro de relação
        /// </summary>
        /// <param name="linha">Linha do registro</param>
        /// <returns>Registro de relação processado</returns>
        private RegistroRelacao ProcessarRegistroRelacao(string linha)
        {
            if (linha.Length < 194)
            {
                throw new ArgumentException("Linha de relação muito curta");
            }

            var registro = new RegistroRelacao
            {
                TipoRegistro = linha.Substring(0, 3),
                Grupo = linha.Substring(3, 1),
                NumeroContrato = linha.Substring(4, 25).Trim(),
                CodigoRelacao = linha.Substring(29, 2).Trim(),
                Saudacao = linha.Substring(31, 3).Trim(),
                Nome = linha.Substring(34, 80).Trim(),
                InformacaoAdicional = linha.Substring(114, 80).Trim()
            };

            return registro;
        }

        /// <summary>
        /// Processa uma linha de registro de acordo
        /// </summary>
        /// <param name="linha">Linha do registro</param>
        /// <returns>Registro de acordo processado</returns>
        private RegistroAcordo ProcessarRegistroAcordo(string linha)
        {
            if (linha.Length < 155)
            {
                throw new ArgumentException("Linha de acordo muito curta");
            }

            var registro = new RegistroAcordo
            {
                TipoRegistro = linha.Substring(0, 1),
                IdAcordo = linha.Substring(1, 13).Trim(),
                DataAcordo = linha.Substring(14, 8).Trim(),
                StatusAcordo = linha.Substring(22, 1),
                TipoAcordo = linha.Substring(23, 8).Trim(),
                IdUsuario = linha.Substring(31, 8).Trim(),
                QuantidadeParcelas = linha.Substring(39, 5).Trim(),
                TaxaJuros = linha.Substring(44, 5).Trim(),
                TipoCotasJuros = linha.Substring(49, 30).Trim(),
                ValorTotalComHonorarios = linha.Substring(79, 15).Trim(),
                ValorTotalSemHonorarios = linha.Substring(94, 15).Trim(),
                ValorHonorarios = linha.Substring(109, 15).Trim(),
                Periodicidade = linha.Substring(124, 30).Trim(),
                NivelAutorizacao = linha.Substring(154, 1)
            };

            return registro;
        }

        /// <summary>
        /// Processa um arquivo de telefones (TELEFONES_OUT.txt)
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Arquivo MFT processado com registros de telefone</returns>
        public async Task<ArquivoMFT<RegistroTelefone>> ProcessarArquivoTelefonesAsync(string caminhoArquivo)
        {
            _logger.LogInformation("Iniciando processamento do arquivo de telefones: {Arquivo}", caminhoArquivo);

            var arquivo = new ArquivoMFT<RegistroTelefone>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                var linhas = await Task.Run(() => ManipuladorArquivos.LerTodasLinhas(caminhoArquivo));

                if (linhas.Length < 2)
                {
                    arquivo.AdicionarErro("Arquivo deve ter pelo menos header e trailer");
                    return arquivo;
                }

                // Processa header
                arquivo.Header = ProcessarHeader(linhas[0]);

                // Processa trailer
                arquivo.Trailer = ProcessarTrailer(linhas[^1]);

                // Processa registros (todas as linhas exceto primeira e última)
                for (int i = 1; i < linhas.Length - 1; i++)
                {
                    var linha = linhas[i];
                    var numeroLinha = i + 1;

                    var resultado = ProcessadorTelefones.ProcessarLinhaTelefone(linha);

                    if (resultado.IsValid)
                    {
                        arquivo.AdicionarRegistro(resultado.Registro);
                        _logger.LogDebug("Linha {NumeroLinha} processada com sucesso", numeroLinha);
                    }
                    else
                    {
                        foreach (var erro in resultado.Erros)
                        {
                            arquivo.AdicionarErro($"Linha {numeroLinha}: {erro}");
                        }
                        _logger.LogWarning("Erro na linha {NumeroLinha}: {Erros}", numeroLinha, string.Join("; ", resultado.Erros));
                    }
                }

                _logger.LogInformation("Processamento concluído. Total de registros: {Total}, Válidos: {Validos}, Erros: {Erros}",
                    arquivo.TotalRegistros, arquivo.RegistrosValidos, arquivo.TotalErros);
            }
            catch (Exception ex)
            {
                arquivo.AdicionarErro($"Erro durante processamento: {ex.Message}");
                _logger.LogError(ex, "Erro ao processar arquivo de telefones: {Arquivo}", caminhoArquivo);
            }

            return arquivo;
        }

        /// <summary>
        /// Processa um arquivo de parcelas (PARCELAS_OUT.txt)
        /// </summary>
        /// <param name="caminhoArquivo">Caminho para o arquivo</param>
        /// <returns>Arquivo MFT processado com registros de parcela</returns>
        public async Task<ArquivoMFT<RegistroParcelaNova>> ProcessarArquivoParcelasAsync(string caminhoArquivo)
        {
            _logger.LogInformation("Iniciando processamento do arquivo de parcelas: {Arquivo}", caminhoArquivo);

            var arquivo = new ArquivoMFT<RegistroParcelaNova>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                var linhas = await Task.Run(() => ManipuladorArquivos.LerTodasLinhas(caminhoArquivo));

                if (linhas.Length < 2)
                {
                    arquivo.AdicionarErro("Arquivo deve ter pelo menos header e trailer");
                    return arquivo;
                }

                // Processa header
                arquivo.Header = ProcessarHeader(linhas[0]);

                // Processa trailer
                arquivo.Trailer = ProcessarTrailer(linhas[^1]);

                // Processa registros (todas as linhas exceto primeira e última)
                for (int i = 1; i < linhas.Length - 1; i++)
                {
                    var linha = linhas[i];
                    var numeroLinha = i + 1;

                    var resultado = ProcessadorParcelas.ProcessarLinhaParcela(linha);

                    if (resultado.IsValid)
                    {
                        arquivo.AdicionarRegistro(resultado.Registro);
                        _logger.LogDebug("Linha {NumeroLinha} processada com sucesso", numeroLinha);
                    }
                    else
                    {
                        foreach (var erro in resultado.Erros)
                        {
                            arquivo.AdicionarErro($"Linha {numeroLinha}: {erro}");
                        }
                        _logger.LogWarning("Erro na linha {NumeroLinha}: {Erros}", numeroLinha, string.Join("; ", resultado.Erros));
                    }
                }

                _logger.LogInformation("Processamento concluído. Total de registros: {Total}, Válidos: {Validos}, Erros: {Erros}",
                    arquivo.TotalRegistros, arquivo.RegistrosValidos, arquivo.TotalErros);
            }
            catch (Exception ex)
            {
                arquivo.AdicionarErro($"Erro durante processamento: {ex.Message}");
                _logger.LogError(ex, "Erro ao processar arquivo de parcelas: {Arquivo}", caminhoArquivo);
            }

            return arquivo;
        }
    }
}