using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using SistemaMFT.Core.Interfaces;
using SistemaMFT.Core.Entities;
using SistemaMFT.Core.Common;
using SistemaMFT.Modules.Telefones.Entities;
using SistemaMFT.Modules.Telefones.Validators;

namespace SistemaMFT.Modules.Telefones.Processors
{
    /// <summary>
    /// Processador específico para arquivos de TELEFONES
    /// Implementa IFileProcessor para manter compatibilidade com a arquitetura modular
    /// Contém toda a lógica de processamento específica para telefones
    /// Independente de outros módulos
    /// Suporta processamento adaptativo para linhas de 77 e 121 caracteres
    /// </summary>
    public class TelefonesProcessor : IFileProcessor<RegistroTelefone>
    {
        private readonly TelefonesValidator _validator;
        private const string ENTIDADE = "TELEFONE";
        private const int TAMANHO_LINHA_PADRAO = 77;
        private const int TAMANHO_LINHA_ESTENDIDO = 121;

        /// <summary>
        /// Construtor do processador de telefones
        /// Inicializa o validador específico
        /// </summary>
        public TelefonesProcessor()
        {
            _validator = new TelefonesValidator();
        }

        /// <summary>
        /// Processa uma linha do arquivo de telefones seguindo o protocolo da documentação
        /// Mantém a mesma lógica do processador original com melhorias na estrutura
        /// Suporta processamento adaptativo para diferentes tamanhos de linha
        /// </summary>
        /// <param name="linha">Linha do arquivo</param>
        /// <param name="numeroLinha">Número da linha para log</param>
        /// <returns>Resultado do processamento da linha</returns>
        public ProcessingResult<RegistroTelefone> ProcessarLinha(string linha, int numeroLinha)
        {
            Console.WriteLine($"\n=== PROCESSANDO LINHA {numeroLinha} - ENTIDADE: {ENTIDADE} ===");

            // Verificar tamanho mínimo da linha
            if (linha.Length < TAMANHO_LINHA_PADRAO)
            {
                var erro = $"Linha muito curta: {linha.Length} caracteres. Mínimo esperado: {TAMANHO_LINHA_PADRAO}";
                Console.WriteLine($"[ERRO] {erro}");
                return ProcessingResult<RegistroTelefone>.Erro(new[] { erro }, numeroLinha, linha);
            }

            // Processamento adaptativo para linhas maiores que o esperado
            string linhaProcessamento = linha;
            if (linha.Length > TAMANHO_LINHA_ESTENDIDO)
            {
                Console.WriteLine($"[AVISO] Linha com {linha.Length} caracteres, truncando para {TAMANHO_LINHA_ESTENDIDO}");
                linhaProcessamento = linha.Substring(0, TAMANHO_LINHA_ESTENDIDO);
            }
            else if (linha.Length > TAMANHO_LINHA_PADRAO && linha.Length < TAMANHO_LINHA_ESTENDIDO)
            {
                Console.WriteLine($"[INFO] Linha com {linha.Length} caracteres, processando formato estendido");
            }

            var telefone = new RegistroTelefone
            {
                NumeroLinha = numeroLinha,
                LinhaOriginal = linha
            };

            try
            {
                // Extrair campos conforme layout da documentação
                ExtrairCampos(linhaProcessamento, telefone);

                // Processar e validar cada campo
                var resultadoProcessamento = ProcessarCampos(telefone);

                if (!resultadoProcessamento.IsValid)
                {
                    return ProcessingResult<RegistroTelefone>.Erro(resultadoProcessamento.Erros, numeroLinha, linha);
                }

                // Validação final usando o validador
                var validacaoFinal = _validator.Validar(telefone);
                
                if (!validacaoFinal.IsValid)
                {
                    return ProcessingResult<RegistroTelefone>.Erro(validacaoFinal.Erros, numeroLinha, linha);
                }

                // Atualizar propriedades derivadas
                telefone.AtualizarPropriedadesDerivadas();

                // Adicionar avisos se houver
                var resultado = ProcessingResult<RegistroTelefone>.Sucesso(telefone, numeroLinha, linha);
                foreach (var aviso in validacaoFinal.Avisos)
                {
                    resultado.AdicionarAviso(aviso);
                }

                Console.WriteLine($"\n[RESUMO ENTIDADE: {ENTIDADE}] Status Geral: SUCESSO | Linha: {numeroLinha} | Telefone: {telefone.TelefoneFormatado}");
                return resultado;
            }
            catch (Exception ex)
            {
                var erro = $"Erro inesperado ao processar linha: {ex.Message}";
                Console.WriteLine($"[ERRO] {erro}");
                return ProcessingResult<RegistroTelefone>.Erro(new[] { erro }, numeroLinha, linha);
            }
        }

        /// <summary>
        /// Processa um arquivo completo de telefones
        /// </summary>
        /// <param name="caminhoArquivo">Caminho completo do arquivo</param>
        /// <returns>Resultado completo do processamento do arquivo</returns>
        public async Task<FileProcessingResult<RegistroTelefone>> ProcessarArquivoAsync(string caminhoArquivo)
        {
            var resultado = new FileProcessingResult<RegistroTelefone>
            {
                NomeArquivo = Path.GetFileName(caminhoArquivo),
                CaminhoArquivo = caminhoArquivo,
                DataProcessamento = DateTime.Now
            };

            try
            {
                if (!File.Exists(caminhoArquivo))
                {
                    resultado.ProcessadoComSucesso = false;
                    return resultado;
                }

                var linhas = await File.ReadAllLinesAsync(caminhoArquivo);
                
                for (int i = 0; i < linhas.Length; i++)
                {
                    var resultadoLinha = ProcessarLinha(linhas[i], i + 1);
                    
                    if (resultadoLinha.IsValid && resultadoLinha.Registro != null)
                    {
                        resultado.RegistrosValidos.Add(resultadoLinha.Registro);
                    }
                    else
                    {
                        resultado.RegistrosComErro.Add(resultadoLinha);
                    }
                }

                resultado.ProcessadoComSucesso = true;
                return resultado;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERRO] Falha ao processar arquivo {caminhoArquivo}: {ex.Message}");
                resultado.ProcessadoComSucesso = false;
                return resultado;
            }
        }

        /// <summary>
        /// Valida se o arquivo está no formato esperado para telefones
        /// </summary>
        /// <param name="caminhoArquivo">Caminho do arquivo</param>
        /// <returns>True se o arquivo é válido para telefones</returns>
        public bool ValidarFormatoArquivo(string caminhoArquivo)
        {
            if (!File.Exists(caminhoArquivo))
                return false;

            var nomeArquivo = Path.GetFileName(caminhoArquivo).ToUpperInvariant();
            return nomeArquivo.Contains("TELEFONES") && nomeArquivo.EndsWith(".TXT");
        }

        /// <summary>
        /// Obtém informações sobre o tipo de arquivo de telefones
        /// </summary>
        /// <returns>Metadados do tipo de arquivo</returns>
        public FileTypeInfo ObterTipoArquivo()
        {
            return new FileTypeInfo
            {
                TipoArquivo = "TELEFONES",
                CodigoRegistro = "701",
                PadraoNome = "*TELEFONES*.txt",
                TamanhoLinha = TAMANHO_LINHA_PADRAO,
                ExtensoesSuportadas = new[] { ".txt" },
                Descricao = "Arquivo de telefones do QuintoAndar/QuintoCred"
            };
        }

        #region Métodos Privados

        /// <summary>
        /// Extrai todos os campos da linha conforme layout da documentação
        /// </summary>
        private void ExtrairCampos(string linha, RegistroTelefone telefone)
        {
            // Layout conforme documentação:
            telefone.TipoRegistro = StringHelper.ExtrairSubstring(linha, 0, 3);           // Posições 1-3
            telefone.Grupo = StringHelper.ExtrairSubstring(linha, 3, 1);                  // Posição 4
            telefone.NumeroContrato = StringHelper.LimparEspacos(StringHelper.ExtrairSubstring(linha, 4, 25));   // Posições 5-29
            telefone.CodigoArea = StringHelper.LimparEspacos(StringHelper.ExtrairSubstring(linha, 29, 3));       // Posições 30-32
            telefone.NumeroTelefone = StringHelper.LimparEspacos(StringHelper.ExtrairSubstring(linha, 32, 15));  // Posições 33-47
            telefone.TipoTelefone = StringHelper.ExtrairSubstring(linha, 47, 1);          // Posição 48
            telefone.Extensao = StringHelper.LimparEspacos(StringHelper.ExtrairSubstring(linha, 48, 10));        // Posições 49-58
            telefone.DataCriacaoTelefone = StringHelper.ExtrairSubstring(linha, 58, 8);           // Posições 59-66
            telefone.StatusTelefone = StringHelper.ExtrairSubstring(linha, 66, 1);        // Posição 67

            // Ranking está nas posições 75-77, mas pode não existir em linhas de 77 caracteres
            if (linha.Length >= 77)
            {
                telefone.Ranking = StringHelper.LimparEspacos(StringHelper.ExtrairSubstring(linha, 74, 3));      // Posições 75-77
            }
        }

        /// <summary>
        /// Processa e valida todos os campos extraídos
        /// Mantém a mesma lógica de log do processador original
        /// </summary>
        private ValidationResult ProcessarCampos(RegistroTelefone telefone)
        {
            var resultado = new ValidationResult { IsValid = true };

            // 1. TIPO DE REGISTRO
            var validacaoTipo = _validator.ValidarCampo(telefone.TipoRegistro, "tiporegistro");
            LogarValidacao("tipoRegistro", telefone.TipoRegistro, "1-3", validacaoTipo);
            if (!validacaoTipo.IsValid) resultado.Erros.Add(validacaoTipo.Erro);

            // 2. GRUPO
            var validacaoGrupo = _validator.ValidarCampo(telefone.Grupo, "grupo");
            LogarValidacao("grupo", telefone.Grupo, "4-4", validacaoGrupo);
            if (!validacaoGrupo.IsValid) resultado.Erros.Add(validacaoGrupo.Erro);

            // 3. NÚMERO DO CONTRATO
            var validacaoContrato = _validator.ValidarCampo(telefone.NumeroContrato, "numerocontrato");
            LogarValidacao("numeroContrato", telefone.NumeroContrato, "5-29", validacaoContrato);
            if (!validacaoContrato.IsValid) resultado.Erros.Add(validacaoContrato.Erro);

            // 4. CÓDIGO DE ÁREA
            var validacaoArea = _validator.ValidarCampo(telefone.CodigoArea, "codigoarea");
            LogarValidacao("codigoArea", telefone.CodigoArea, "30-32", validacaoArea);
            if (!validacaoArea.IsValid) resultado.Erros.Add(validacaoArea.Erro);

            // 5. NÚMERO DO TELEFONE
            var validacaoNumero = _validator.ValidarCampo(telefone.NumeroTelefone, "numerotelefone");
            LogarValidacao("numeroTelefone", telefone.NumeroTelefone, "33-47", validacaoNumero);
            if (!validacaoNumero.IsValid) resultado.Erros.Add(validacaoNumero.Erro);

            // 6. TIPO DE TELEFONE
            var validacaoTipoTel = _validator.ValidarCampo(telefone.TipoTelefone, "tipotelefone");
            LogarValidacao("tipoTelefone", telefone.TipoTelefone, "48-48", validacaoTipoTel);
            if (!validacaoTipoTel.IsValid) resultado.Erros.Add(validacaoTipoTel.Erro);

            // 7. EXTENSÃO (opcional)
            var validacaoExtensao = _validator.ValidarCampo(telefone.Extensao, "extensao");
            LogarValidacao("extensao", telefone.Extensao, "49-58", validacaoExtensao);
            if (!validacaoExtensao.IsValid) resultado.Erros.Add(validacaoExtensao.Erro);

            // 8. DATA DE CRIAÇÃO
            var validacaoData = _validator.ValidarCampo(telefone.DataCriacaoTelefone, "datacriacao");
            LogarValidacao("dataCriacao", telefone.DataCriacaoTelefone, "59-66", validacaoData);
            if (!validacaoData.IsValid) resultado.Erros.Add(validacaoData.Erro);

            // 9. STATUS DO TELEFONE
            var validacaoStatus = _validator.ValidarCampo(telefone.StatusTelefone, "statustelefone");
            LogarValidacao("statusTelefone", telefone.StatusTelefone, "67-67", validacaoStatus);
            if (!validacaoStatus.IsValid) resultado.Erros.Add(validacaoStatus.Erro);

            // 10. RANKING (opcional)
            if (!string.IsNullOrEmpty(telefone.Ranking))
            {
                var validacaoRanking = _validator.ValidarCampo(telefone.Ranking, "ranking");
                LogarValidacao("ranking", telefone.Ranking, "75-77", validacaoRanking);
                if (!validacaoRanking.IsValid) resultado.Erros.Add(validacaoRanking.Erro);
            }

            resultado.IsValid = resultado.Erros.Count == 0;
            return resultado;
        }

        /// <summary>
        /// Loga o resultado da validação de um campo específico
        /// Mantém o mesmo formato de log do processador original
        /// </summary>
        private void LogarValidacao(string nomeCampo, string valor, string posicao, FieldValidationResult validacao)
        {
            var status = validacao.IsValid ? "VÁLIDO" : "INVÁLIDO";
            var erro = validacao.IsValid ? "Nenhum" : validacao.Erro;
            
            Console.WriteLine($"[ENTIDADE: {ENTIDADE}] {nomeCampo}: '{valor}' | Posição: {posicao} | Status: {status} | Erro: {erro}");
        }

        #endregion
    }
}
