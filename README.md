# Sistema MFT - QuintoAndar

## 📖 Descrição

Sistema console em C# para processamento de arquivos MFT (Managed File Transfer) e integração com APIs CyberAgreements. Desenvolvido especificamente para processar layouts de arquivos conforme especificação QuintoAndar/QuintoCred.

## 🎯 Funcionalidades

### ✅ Implementadas
- ✅ Estrutura completa de classes e entidades
- ✅ Processamento de arquivos MFT com codificação ISO-8859-1
- ✅ **Processamento de Contratos**: Layout 155 caracteres (código 200)
- ✅ **Processamento de Telefones**: Layout 77 caracteres (código 701)
- ✅ **Processamento de Parcelas**: Layout 106 caracteres (código 000)
- ✅ Validação de headers e trailers
- ✅ Validação de CPF/CNPJ com dígitos verificadores
- ✅ Validação de telefones (códigos de área 11-99, números 8-9 dígitos)
- ✅ Conversões de dados (datas MMDDAAAA, valores monetários)
- ✅ Cálculos financeiros (valor total, dias de atraso, situação)
- ✅ Sistema de configuração via JSON
- ✅ Logging estruturado com saída formatada por campo
- ✅ Manipulação de arquivos com backup automático

### 🚧 Para Implementar
- 🔲 Conexão SFTP para download/upload de arquivos
- 🔲 Integração completa com APIs CyberAgreements
- 🔲 Criptografia/descriptografia GPG
- 🔲 Geração de relatórios
- 🔲 Limpeza automática de diretórios
- 🔲 Processamento automático por agenda

## 🗂️ Estrutura do Projeto

```
SistemaMFT/
├── 📁 Configuracao/           # Classes de configuração
│   ├── ConfiguracaoMFT.cs     # Configurações principais
│   ├── ConfiguracaoAPI.cs     # Configurações de APIs
│   └── 📁 chaves/             # Chaves GPG
│       ├── chave_publica.asc
│       └── chave_privada.asc
├── 📁 Entidades/              # Modelos de dados
│   ├── HeaderArquivo.cs       # Cabeçalho padrão
│   ├── TrailerArquivo.cs      # Rodapé padrão
│   ├── ArquivoMFT.cs         # Classe base para arquivos
│   ├── RegistroContrato.cs    # CONTRATOS_OUT.txt
│   ├── RegistroParcela.cs     # PARCELAS_OUT.txt
│   ├── RegistroRelacao.cs     # RELATIONS_OUT.txt
│   └── RegistroAcordo.cs      # ACORDOS_OUT.txt
├── 📁 Servicos/               # Lógica de negócio
│   └── ProcessadorArquivosMFT.cs
├── 📁 Utilitarios/            # Funções auxiliares
│   ├── ConversorDados.cs      # Conversões e validações
│   └── ManipuladorArquivos.cs # Manipulação de arquivos
├── Program.cs                 # Programa principal
├── appsettings.json          # Configurações
├── SistemaMFT.csproj         # Projeto C#
└── README.md                 # Este arquivo
```

## ⚙️ Configuração

### 1. Credenciais (appsettings.json)

```json
{
  "ConfiguracaoMFT": {
    "ServidorMFT": {
      "Host": "mft.toppen.com.br",
      "Porta": 8448
    },
    "ServidorSFTP": {
      "Host": "mft.toppen.com.br",
      "Porta": 228,
      "Usuario": "gondim",
      "Senha": "M@caco13",
      "DiretorioRemoto": "/homologacao/gpg/"
    },
    "ConfiguracaoGPG": {
      "EmailDestinatario": "<EMAIL>"
    }
  },
  "ConfiguracaoAPI": {
    "CyberAgreements": {
      "Host": "cyber27h01.toppen.com.br",
      "Authorization": "Basic UUFfR1JCOkBRdWludG8xMjQ=",
      "TimeoutMinutos": 15
    }
  }
}
```

### 2. Estrutura de Diretórios

O sistema criará automaticamente:
- `Arquivos/Entrada/` - Arquivos para processar
- `Arquivos/Saida/` - Arquivos gerados
- `Arquivos/Processados/` - Arquivos já processados
- `Arquivos/Erros/` - Arquivos com erro

## 🚀 Como Usar

### 1. Compilar e Executar

```bash
# Compilar o projeto
dotnet build

# Executar o sistema
dotnet run
```

### 2. Menu Principal

```
═══════════════════════════════════════════════════════════════
                       MENU PRINCIPAL                          
═══════════════════════════════════════════════════════════════

1. 📁 Processar Arquivos MFT
2. 🔗 Testar Conexões (SFTP/MFT/API)
3. ⚙️  Exibir Configuração
4. 🧹 Limpar Diretórios
5. 📊 Gerar Relatórios
0. ❌ Sair
```

### 3. Processamento de Arquivos

1. Coloque os arquivos `.txt` no diretório `Arquivos/Entrada/`
2. Execute a opção **1** do menu
3. Escolha processar arquivo específico ou todos
4. Acompanhe o progresso na tela
5. Arquivos processados são movidos automaticamente

## 📋 Layouts de Arquivos Suportados

### CONTRATOS_OUT.txt / CONTRATOS_FULL_OUT.txt
- 583 caracteres por linha
- Contém dados de contratos, clientes e valores
- Frequência: Diário / Semanal

### PARCELAS_OUT.txt / PARCELAS_FULL_OUT.txt
- 228 caracteres por linha
- Contém parcelas, valores e status
- Frequência: Diário / Semanal

### RELATIONS_OUT.txt
- 194 caracteres por linha
- Contém avalistas, fiadores e garantidores
- Frequência: Diário

### ACORDOS_OUT.txt
- 155 caracteres por linha
- Contém acordos e negociações
- Frequência: Diário

## 🔧 APIs CyberAgreements

### Sequência Obrigatória:
1. `atualizaSaldo`
2. `consultaElegibilidade`
3. `consultaAmortizacao`
4. `cadastraAcordo`
5. `geraMeioPagamento`
6. `finalizarSessao`

### Timeout: 15 minutos

## 📊 Validações Implementadas

### Arquivos:
- ✅ Header inicia com "H"
- ✅ Trailer inicia com "T"
- ✅ Quantidade de registros confere
- ✅ Datas de header e trailer iguais
- ✅ Codificação ISO-8859-1

### Dados:
- ✅ Conversão de datas DDMMYYYY e MMDDYYYY
- ✅ Conversão de valores monetários (centavos)
- ✅ Validação de CPF e CNPJ
- ✅ Grupos válidos (1=QuintoAndar, 2=QuintoCred)

## 🔐 Segurança

### Chaves GPG:
- Chave pública: `Configuracao/chaves/chave_publica.asc`
- Chave privada: `Configuracao/chaves/chave_privada.asc`
- Email: `<EMAIL>`

### Credenciais:
- Todas as credenciais ficam no `appsettings.json`
- ⚠️ **NÃO commitar credenciais reais em repositórios**

## 📝 Logs

O sistema gera logs estruturados no console mostrando:
- ✅ Arquivos processados com sucesso
- ❌ Erros encontrados
- 📊 Estatísticas de processamento
- 🔍 Detalhes de validação

## 🛠️ Dependências

```xml
<PackageReference Include="SSH.NET" Version="2020.0.2" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="PgpCore" Version="6.2.4" />
<PackageReference Include="System.Text.Encoding.CodePages" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
```

## 🐛 Solução de Problemas

### Erro de Codificação
```
Solução: Verificar se arquivo está em ISO-8859-1
```

### Arquivo não Processado
```
Verificar:
1. Estrutura header/trailer
2. Tamanho das linhas
3. Formato dos campos
4. Logs de erro detalhados
```

### Conexão SFTP
```
Verificar:
1. Host e porta
2. Usuário e senha
3. Diretório remoto
4. Firewall/rede
```

## 📞 Suporte

- **Sistema**: Console C# .NET 6.0
- **Codificação**: ISO-8859-1
- **Plataforma**: Windows/Linux/macOS
- **Idioma**: Português

## 🤖 NOVO: Sistema de Automação Inteligente

### Processamento Conforme Documentação Oficial

Implementamos um sistema de automação completo seguindo exatamente a documentação fornecida:

#### ✅ Validadores Universais Implementados
- **CPF** - Algoritmo completo com dígitos verificadores
- **CNPJ** - Validação empresarial completa
- **Datas MMDDAAAA** - Validação de existência e consistência
- **Valores Monetários** - Decimais implícitas e formatação
- **Status** - Validação contra valores permitidos

#### ✅ Processador de Contratos (Layout 155 caracteres)
- **Código de Registro**: "100"
- **Protocolo de 5 passos**: Extração → Limpeza → Validação → Log → Mapeamento
- **Console.log formatado** para cada campo extraído
- **Validação posicional precisa**

### Como Usar o Novo Sistema

#### Executar Exemplo Demonstrativo
```bash
dotnet run exemplo
```

#### Exemplo de Saída
```
=== PROCESSANDO LINHA 1 - ENTIDADE: CONTRATO ===
[ENTIDADE: CONTRATO] tipoRegistro: '100' | Posição: 1-3 | Status: VÁLIDO | Erro: Nenhum
[ENTIDADE: CONTRATO] grupo: '1' | Posição: 4-4 | Status: VÁLIDO | Erro: Nenhum
[ENTIDADE: CONTRATO] numeroContrato: '12345678901234567890123' | Posição: 5-29 | Status: VÁLIDO | Erro: Nenhum
[ENTIDADE: CONTRATO] cpfCnpj: '12345678901' | Posição: 30-43 | Status: VÁLIDO | Erro: Nenhum

[RESUMO ENTIDADE: CONTRATO] Status Geral: SUCESSO | Linha: 1 | Contrato: 12345678901234567890123

✅ CONTRATO PROCESSADO COM SUCESSO
📋 Número do Contrato: 12345678901234567890123
👤 Cliente: JOÃO DA SILVA SANTOS
📄 Documento: 123.456.789-01 (CPF)
🏢 Grupo: QuintoAndar - Locação
📊 Status: Ativo
📅 Período: 31/12/2023 até 31/12/2024
💰 Valor: R$ 1.250,00
```

#### Layout do Arquivo CONTRATOS (155 caracteres)
- **Posição 1-3**: Tipo registro ("100")
- **Posição 4**: Grupo (1=QuintoAndar, 2=QuintoCred)
- **Posição 5-29**: Número do contrato
- **Posição 30-43**: CPF/CNPJ (validação completa)
- **Posição 44-123**: Nome do cliente
- **Posição 124**: Status (A/I/C)
- **Posição 125-132**: Data início (MMDDAAAA)
- **Posição 133-140**: Data fim (MMDDAAAA)
- **Posição 141-155**: Valor do contrato

### Status da Implementação
- ✅ **Validadores Universais** - Completo
- ✅ **Processador de Contratos** - Completo
- ✅ **Sistema de Logs Detalhados** - Completo
- ✅ **Exemplo de Uso** - Completo
- 🔄 **Outros tipos de arquivo** - Em desenvolvimento

---

**Desenvolvido para QuintoAndar** 🏠
*Sistema MFT - Versão 1.0.0 com Automação Inteligente*