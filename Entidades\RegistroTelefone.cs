using System;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa um registro de telefone do arquivo TELEFONES_OUT.txt
    /// Layout: 77 caracteres por linha
    /// Código de Registro: "701"
    /// </summary>
    public class RegistroTelefone
    {
        // Campos básicos (posições 1-29)
        public string TipoRegistro { get; set; } = string.Empty; // Posição 1-3: "701"
        public string Grupo { get; set; } = string.Empty; // Posição 4: 1=QuintoAndar, 2=QuintoCred
        public string NumeroContrato { get; set; } = string.Empty; // Posição 5-29

        // Dados do telefone (posições 30-77)
        public string CodigoArea { get; set; } = string.Empty; // Posição 30-32
        public string NumeroTelefone { get; set; } = string.Empty; // Posição 33-47
        public string TipoTelefone { get; set; } = string.Empty; // Posição 48: 0/1/2/4/5
        public string Extensao { get; set; } = string.Empty; // Posição 49-58
        public string DataCriacao { get; set; } = string.Empty; // Posição 59-66: MMDDAAAA
        public string StatusTelefone { get; set; } = string.Empty; // Posição 67: A/I
        public string Ranking { get; set; } = string.Empty; // Posição 75-77

        // Propriedades derivadas
        public string GrupoDescricao => Grupo switch
        {
            "1" => "QuintoAndar - Locação",
            "2" => "QuintoCred - Crédito",
            _ => "Grupo Desconhecido"
        };

        public string TipoDescricao => TipoTelefone switch
        {
            "0" => "Outro",
            "1" => "Casa",
            "2" => "Trabalho",
            "4" => "Familiar",
            "5" => "Celular",
            _ => "Tipo Desconhecido"
        };

        public string StatusDescricao => StatusTelefone switch
        {
            "A" => "Ativo",
            "I" => "Inativo",
            _ => "Status Desconhecido"
        };

        // Formatação do telefone
        public string TelefoneCompleto
        {
            get
            {
                var numeroLimpo = NumeroTelefone.Replace("-", "").Replace(" ", "").Trim();
                if (numeroLimpo.Length == 9)
                {
                    return $"({CodigoArea}) {numeroLimpo.Substring(0, 5)}-{numeroLimpo.Substring(5)}";
                }
                else if (numeroLimpo.Length == 8)
                {
                    return $"({CodigoArea}) {numeroLimpo.Substring(0, 4)}-{numeroLimpo.Substring(4)}";
                }
                return $"({CodigoArea}) {NumeroTelefone}";
            }
        }

        // Formatação da data
        public string DataCriacaoFormatada
        {
            get
            {
                if (DataCriacao.Length == 8 && DateTime.TryParseExact(DataCriacao, "MMddyyyy", null, System.Globalization.DateTimeStyles.None, out DateTime data))
                {
                    return data.ToString("dd/MM/yyyy");
                }
                return DataCriacao;
            }
        }

        // Ranking numérico
        public int? RankingNumerico
        {
            get
            {
                if (int.TryParse(Ranking, out int rank))
                {
                    return rank;
                }
                return null;
            }
        }
    }
}
