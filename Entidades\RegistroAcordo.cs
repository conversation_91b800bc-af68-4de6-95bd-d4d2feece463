using System.ComponentModel.DataAnnotations;

namespace SistemaMFT.Entidades
{
    /// <summary>
    /// Representa um registro do arquivo ACORDOS_OUT.txt
    /// Layout conforme especificação: 155 caracteres por linha
    /// </summary>
    public class RegistroAcordo
    {
        /// <summary>
        /// Tipo de registro - posição 1 (sempre "1")
        /// </summary>
        [Required]
        [StringLength(1)]
        public string TipoRegistro { get; set; } = "1";

        /// <summary>
        /// ID do acordo - posições 2-14 (13 caracteres)
        /// </summary>
        [Required]
        [StringLength(13)]
        public string IdAcordo { get; set; } = string.Empty;

        /// <summary>
        /// Data do acordo - posições 15-22 (8 caracteres - formato MMDDYYYY)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string DataAcordo { get; set; } = string.Empty;

        /// <summary>
        /// Status do acordo - posição 23 (1 caractere)
        /// A=Aprovado, P=Pendente, C=Cancelado, F=Finalizado
        /// </summary>
        [Required]
        [StringLength(1)]
        public string StatusAcordo { get; set; } = string.Empty;

        /// <summary>
        /// Tipo de acordo - posições 24-31 (8 caracteres)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string TipoAcordo { get; set; } = string.Empty;

        /// <summary>
        /// ID do usuário - posições 32-39 (8 caracteres)
        /// </summary>
        [Required]
        [StringLength(8)]
        public string IdUsuario { get; set; } = string.Empty;

        /// <summary>
        /// Quantidade de parcelas - posições 40-44 (5 caracteres)
        /// </summary>
        [Required]
        [StringLength(5)]
        public string QuantidadeParcelas { get; set; } = string.Empty;

        /// <summary>
        /// Taxa de juros - posições 45-49 (5 caracteres)
        /// </summary>
        [Required]
        [StringLength(5)]
        public string TaxaJuros { get; set; } = string.Empty;

        /// <summary>
        /// Tipo de cotas de juros - posições 50-79 (30 caracteres)
        /// NO_INT, EQUAL, INCR, DECR
        /// </summary>
        [Required]
        [StringLength(30)]
        public string TipoCotasJuros { get; set; } = string.Empty;

        /// <summary>
        /// Valor total com honorários - posições 80-94 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorTotalComHonorarios { get; set; } = string.Empty;

        /// <summary>
        /// Valor total sem honorários - posições 95-109 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorTotalSemHonorarios { get; set; } = string.Empty;

        /// <summary>
        /// Valor dos honorários - posições 110-124 (15 caracteres)
        /// </summary>
        [Required]
        [StringLength(15)]
        public string ValorHonorarios { get; set; } = string.Empty;

        /// <summary>
        /// Periodicidade - posições 125-154 (30 caracteres)
        /// WEEKLY, DAILY, MONTHLY, BIMONTHLY, QUARTERLY
        /// </summary>
        [Required]
        [StringLength(30)]
        public string Periodicidade { get; set; } = string.Empty;

        /// <summary>
        /// Nível de autorização - posição 155 (1 caractere)
        /// </summary>
        [Required]
        [StringLength(1)]
        public string NivelAutorizacao { get; set; } = string.Empty;

        /// <summary>
        /// Converte data do acordo (MMDDYYYY) para DateTime
        /// </summary>
        /// <returns>Data do acordo como DateTime</returns>
        public DateTime ObterDataAcordo()
        {
            if (string.IsNullOrEmpty(DataAcordo) || DataAcordo.Length != 8)
            {
                throw new ArgumentException("Data do acordo deve ter 8 dígitos no formato MMDDYYYY");
            }

            var mes = int.Parse(DataAcordo.Substring(0, 2));
            var dia = int.Parse(DataAcordo.Substring(2, 2));
            var ano = int.Parse(DataAcordo.Substring(4, 4));

            return new DateTime(ano, mes, dia);
        }

        /// <summary>
        /// Obtém quantidade de parcelas como inteiro
        /// </summary>
        /// <returns>Quantidade de parcelas</returns>
        public int ObterQuantidadeParcelas()
        {
            if (string.IsNullOrEmpty(QuantidadeParcelas))
                return 0;

            return int.Parse(QuantidadeParcelas.Trim());
        }

        /// <summary>
        /// Obtém taxa de juros como decimal
        /// </summary>
        /// <returns>Taxa de juros</returns>
        public decimal ObterTaxaJuros()
        {
            if (string.IsNullOrEmpty(TaxaJuros))
                return 0;

            var taxa = decimal.Parse(TaxaJuros.Trim());
            return taxa / 100.00m; // Assumindo que vem em formato percentual
        }

        /// <summary>
        /// Converte valor total com honorários para decimal
        /// </summary>
        /// <returns>Valor total com honorários</returns>
        public decimal ObterValorTotalComHonorarios()
        {
            if (string.IsNullOrEmpty(ValorTotalComHonorarios))
                return 0;

            var valor = long.Parse(ValorTotalComHonorarios.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte valor total sem honorários para decimal
        /// </summary>
        /// <returns>Valor total sem honorários</returns>
        public decimal ObterValorTotalSemHonorarios()
        {
            if (string.IsNullOrEmpty(ValorTotalSemHonorarios))
                return 0;

            var valor = long.Parse(ValorTotalSemHonorarios.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Converte valor dos honorários para decimal
        /// </summary>
        /// <returns>Valor dos honorários</returns>
        public decimal ObterValorHonorarios()
        {
            if (string.IsNullOrEmpty(ValorHonorarios))
                return 0;

            var valor = long.Parse(ValorHonorarios.Trim());
            return valor / 100.00m;
        }

        /// <summary>
        /// Obtém a descrição do status do acordo
        /// </summary>
        /// <returns>Descrição do status</returns>
        public string ObterDescricaoStatus()
        {
            return StatusAcordo switch
            {
                "A" => "Aprovado",
                "P" => "Pendente",
                "C" => "Cancelado",
                "F" => "Finalizado",
                _ => "Status Desconhecido"
            };
        }

        /// <summary>
        /// Obtém a descrição da periodicidade
        /// </summary>
        /// <returns>Descrição da periodicidade</returns>
        public string ObterDescricaoPeriodicidade()
        {
            return Periodicidade.Trim() switch
            {
                "WEEKLY" => "Semanal",
                "DAILY" => "Diário",
                "MONTHLY" => "Mensal",
                "BIMONTHLY" => "Bimestral",
                "QUARTERLY" => "Trimestral",
                _ => "Periodicidade Desconhecida"
            };
        }

        /// <summary>
        /// Obtém a descrição do tipo de cotas de juros
        /// </summary>
        /// <returns>Descrição do tipo de cotas</returns>
        public string ObterDescricaoTipoCotasJuros()
        {
            return TipoCotasJuros.Trim() switch
            {
                "NO_INT" => "Sem Juros",
                "EQUAL" => "Juros Iguais",
                "INCR" => "Juros Crescentes",
                "DECR" => "Juros Decrescentes",
                _ => "Tipo de Cotas Desconhecido"
            };
        }

        /// <summary>
        /// Valida se o registro está correto conforme regras mínimas
        /// </summary>
        /// <returns>True se válido, False caso contrário</returns>
        public bool ValidarRegistro()
        {
            return TipoRegistro == "1" &&
                   !string.IsNullOrEmpty(IdAcordo) &&
                   "APCF".Contains(StatusAcordo) &&
                   !string.IsNullOrEmpty(DataAcordo) &&
                   DataAcordo.Length == 8;
        }
    }
} 