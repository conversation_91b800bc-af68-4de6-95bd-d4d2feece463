using System.Collections.Generic;

namespace SistemaMFT.Core.Interfaces
{
    /// <summary>
    /// Interface para validadores de entidades
    /// Define o contrato para validação de dados específicos de cada módulo
    /// Permite que cada módulo tenha suas próprias regras de validação independentes
    /// </summary>
    /// <typeparam name="TEntity">Tipo da entidade a ser validada</typeparam>
    public interface IValidator<TEntity> where TEntity : class
    {
        /// <summary>
        /// Valida uma entidade e retorna o resultado da validação
        /// Cada módulo implementa suas próprias regras de negócio
        /// </summary>
        /// <param name="entidade">Entidade a ser validada</param>
        /// <returns>Resultado da validação com erros e avisos</returns>
        ValidationResult Validar(TEntity entidade);

        /// <summary>
        /// Valida um campo específico da entidade
        /// Permite validação granular de campos individuais
        /// </summary>
        /// <param name="valor">Valor do campo a ser validado</param>
        /// <param name="nomeCampo">Nome do campo para identificação</param>
        /// <returns>Resultado da validação do campo específico</returns>
        FieldValidationResult ValidarCampo(string valor, string nomeCampo);

        /// <summary>
        /// Obtém todas as regras de validação aplicáveis a esta entidade
        /// Usado para documentação e debugging das validações
        /// </summary>
        /// <returns>Lista de regras de validação</returns>
        IEnumerable<ValidationRule> ObterRegrasValidacao();
    }

    /// <summary>
    /// Resultado da validação de uma entidade
    /// Contém informações sobre sucesso, erros e avisos
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Erros { get; set; } = new List<string>();
        public List<string> Avisos { get; set; } = new List<string>();
    }

    /// <summary>
    /// Resultado da validação de um campo específico
    /// Usado para validações granulares
    /// </summary>
    public class FieldValidationResult
    {
        public bool IsValid { get; set; }
        public string Erro { get; set; } = string.Empty;
        public string ValorFormatado { get; set; } = string.Empty;
    }

    /// <summary>
    /// Representa uma regra de validação
    /// Usado para documentação das validações aplicadas
    /// </summary>
    public class ValidationRule
    {
        public string Campo { get; set; } = string.Empty;
        public string Regra { get; set; } = string.Empty;
        public string Descricao { get; set; } = string.Empty;
    }
}
